buildscript {
    dependencies {
        classpath 'com.android.tools.build:gradle:8.6.0'
    }
    repositories {
        google()
        mavenCentral()
    }
}
allprojects {
    repositories {
        google()
        mavenCentral()
        // Removed background_fetch - using native Android scheduling
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"

    afterEvaluate {
        android {
            compileSdkVersion 35
        }
    }
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}