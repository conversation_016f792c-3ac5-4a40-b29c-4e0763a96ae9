<resources>
    <!-- Launch theme for splash screen -->
    <style name="LaunchTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@drawable/launch_background</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <item name="fontFamily">@font/tajawal_regular</item>
    </style>

    <!-- Main app theme -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="android:windowBackground">?android:colorBackground</item>
        <item name="fontFamily">@font/tajawal_regular</item>
    </style>

    <!-- Normal app theme without ActionBar -->
    <style name="NormalTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">?android:colorBackground</item>
        <item name="fontFamily">@font/tajawal_regular</item>
    </style>

    <!-- Text styles -->
    <style name="NormalTextStyle">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:gravity">center</item>
        <item name="fontFamily">@font/tajawal_regular</item>
    </style>

    <style name="HighlightedTextStyle">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/yellow</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="fontFamily">@font/tajawal_regular</item>
    </style>
</resources>


        <!--<resources>-->
<!--    <style name="NormalTextStyle">-->
<!--        <item name="android:textSize">14sp</item>-->
<!--        <item name="android:textColor">@android:color/white</item>-->
<!--        <item name="android:gravity">center</item>-->
<!--        <item name="android:fontFamily">sans-serif</item> &lt;!&ndash; Use system font &ndash;&gt;-->
<!--    </style>-->

<!--    <style name="HighlightedTextStyle">-->
<!--        <item name="android:textSize">16sp</item>-->
<!--        <item name="android:textColor">@color/yellow</item>-->
<!--        <item name="android:textStyle">bold</item>-->
<!--        <item name="android:gravity">center</item>-->
<!--        <item name="android:fontFamily">sans-serif</item> &lt;!&ndash; Use system font &ndash;&gt;-->
<!--    </style>-->
<!--</resources>-->
<!--<?xml version="1.0" encoding="utf-8"?>-->
<!--<resources>-->

<!--    &lt;!&ndash; Default text style for all prayers &ndash;&gt;-->
<!--    <style name="NormalTextStyle">-->
<!--        <item name="android:textSize">14sp</item>-->
<!--        <item name="android:textColor">@android:color/white</item>-->
<!--        <item name="android:gravity">center</item>-->
<!--        <item name="android:fontFamily">@font/cairo</item>-->
<!--    </style>-->

<!--    &lt;!&ndash; Highlighted style for next prayer &ndash;&gt;-->
<!--    <style name="HighlightedTextStyle">-->
<!--        <item name="android:textSize">16sp</item>-->
<!--        <item name="android:textColor">@color/yellow</item>-->
<!--        <item name="android:textStyle">bold</item>-->
<!--        <item name="android:gravity">center</item>-->
<!--        <item name="android:fontFamily">@font/cairo</item>-->
<!--    </style>-->

<!--    &lt;!&ndash; Cairo font styling for prayers (for non-widget layouts, if needed) &ndash;&gt;-->
<!--    <style name="CairoTextStyle">-->
<!--        <item name="android:textSize">14sp</item>-->
<!--        <item name="android:textColor">@android:color/black</item>-->
<!--        <item name="android:fontFamily">@font/cairo</item>-->
<!--        <item name="android:gravity">center</item>-->
<!--    </style>-->

<!--</resources>-->

<!--        <?xml version="1.0" encoding="utf-8"?>-->
<!--<resources>-->
<!--    <style name="NormalTextStyle">-->
<!--        <item name="android:textSize">14sp</item>-->
<!--        <item name="android:textColor">@android:color/white</item>-->
<!--        <item name="android:fontFamily">@font/cairo</item>-->
<!--    </style>-->
<!--    <style name="HighlightedTextStyle">-->
<!--        <item name="android:textSize">16sp</item>-->
<!--        <item name="android:textColor">@color/yellow</item>-->
<!--        <item name="android:textStyle">bold</item>-->
<!--        <item name="android:fontFamily">@font/cairo</item>-->
<!--    </style>-->
<!--    <style name="CairoTextStyle">-->
<!--        <item name="android:fontFamily">@font/cairo</item>-->
<!--        <item name="android:textSize">14sp</item>-->
<!--        <item name="android:textColor">@android:color/black</item>-->
<!--    </style>-->
<!--</resources>-->
<!--<resources>-->
<!--    &lt;!&ndash; App Themes &ndash;&gt;-->
<!--    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">-->
<!--        <item name="android:windowBackground">@drawable/launch_background</item>-->
<!--        <item name="android:forceDarkAllowed">false</item>-->
<!--        <item name="android:windowFullscreen">false</item>-->
<!--        <item name="android:windowDrawsSystemBarBackgrounds">false</item>-->
<!--        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>-->
<!--    </style>-->

<!--    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">-->
<!--        <item name="android:windowBackground">?android:colorBackground</item>-->
<!--    </style>-->

<!--    &lt;!&ndash; Widget Styles &ndash;&gt;-->
<!--    <style name="PrayerTimeStyle">-->
<!--        <item name="android:textSize">16sp</item>-->
<!--        <item name="android:textColor">@android:color/white</item>-->
<!--        <item name="android:layout_width">wrap_content</item>-->
<!--        <item name="android:layout_height">wrap_content</item>-->
<!--        <item name="android:layout_marginBottom">8dp</item>-->
<!--    </style>-->

<!--    <style name="NextPrayerTimeStyle" parent="PrayerTimeStyle">-->
<!--        <item name="android:textColor">@android:color/holo_red_light</item>-->
<!--        <item name="android:textStyle">bold</item>-->
<!--    </style>-->
<!--</resources>-->

        <!--<?xml version="1.0" encoding="utf-8"?>-->
<!--<resources>-->
<!--    &lt;!&ndash; Theme applied to the Android Window while the process is starting when the OS's Dark Mode setting is off &ndash;&gt;-->
<!--    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">-->
<!--        &lt;!&ndash; Show a splash screen on the activity. Automatically removed when-->
<!--             the Flutter engine draws its first frame &ndash;&gt;-->
<!--        <item name="android:windowBackground">@drawable/launch_background</item>-->
<!--        <item name="android:forceDarkAllowed">false</item>-->
<!--        <item name="android:windowFullscreen">false</item>-->
<!--        <item name="android:windowDrawsSystemBarBackgrounds">false</item>-->
<!--        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>-->
<!--    </style>-->
<!--    &lt;!&ndash; Theme applied to the Android Window as soon as the process has started.-->
<!--         This theme determines the color of the Android Window while your-->
<!--         Flutter UI initializes, as well as behind your Flutter UI while its-->
<!--         running.-->

<!--         This Theme is only used starting with V2 of Flutter's Android embedding. &ndash;&gt;-->
<!--    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">-->
<!--        <item name="android:windowBackground">?android:colorBackground</item>-->
<!--    </style>-->
<!--</resources>-->
