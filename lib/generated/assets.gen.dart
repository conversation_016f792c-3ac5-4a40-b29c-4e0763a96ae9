/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';

class $AssetsFontsGen {
  const $AssetsFontsGen();

  /// File path: assets/fonts/arsura.ttf
  String get arsura => 'assets/fonts/arsura.ttf';

  /// List of all assets
  List<String> get values => [arsura];
}

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/live.png
  AssetGenImage get live => const AssetGenImage('assets/icons/live.png');

  /// List of all assets
  List<AssetGenImage> get values => [live];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/app_logo.png
  AssetGenImage get appLogo =>
      const AssetGenImage('assets/images/app_logo.png');

  /// File path: assets/images/audio_stream_logo.png
  AssetGenImage get audioStreamLogo =>
      const AssetGenImage('assets/images/audio_stream_logo.png');

  /// File path: assets/images/audio_waves.png
  AssetGenImage get audioWaves =>
      const AssetGenImage('assets/images/audio_waves.png');

  /// File path: assets/images/background.png
  AssetGenImage get background =>
      const AssetGenImage('assets/images/background.png');

  /// File path: assets/images/banner.webp
  AssetGenImage get banner => const AssetGenImage('assets/images/banner.webp');

  /// File path: assets/images/basmala.png
  AssetGenImage get basmala => const AssetGenImage('assets/images/basmala.png');

  /// File path: assets/images/compass.svg
  String get compass => 'assets/images/compass.svg';

  /// File path: assets/images/header.png
  AssetGenImage get header => const AssetGenImage('assets/images/header.png');

  /// File path: assets/images/home_logo.png
  AssetGenImage get homeLogo =>
      const AssetGenImage('assets/images/home_logo.png');

  /// File path: assets/images/logo.png
  AssetGenImage get logo => const AssetGenImage('assets/images/logo.png');

  /// File path: assets/images/native_splash.png
  AssetGenImage get nativeSplash =>
      const AssetGenImage('assets/images/native_splash.png');

  /// File path: assets/images/needle.svg
  String get needle => 'assets/images/needle.svg';

  /// File path: assets/images/quran_background.png
  AssetGenImage get quranBackground =>
      const AssetGenImage('assets/images/quran_background.png');

  /// File path: assets/images/quran_cover.png
  AssetGenImage get quranCover =>
      const AssetGenImage('assets/images/quran_cover.png');

  /// File path: assets/images/quran_header.png
  AssetGenImage get quranHeader =>
      const AssetGenImage('assets/images/quran_header.png');

  /// File path: assets/images/splash.gif
  AssetGenImage get splash => const AssetGenImage('assets/images/splash.gif');

  /// File path: assets/images/text_background.png
  AssetGenImage get textBackground =>
      const AssetGenImage('assets/images/text_background.png');

  /// List of all assets
  List<dynamic> get values => [
        appLogo,
        audioStreamLogo,
        audioWaves,
        background,
        banner,
        basmala,
        compass,
        header,
        homeLogo,
        logo,
        nativeSplash,
        needle,
        quranBackground,
        quranCover,
        quranHeader,
        splash,
        textBackground
      ];
}

class $AssetsJsonGen {
  const $AssetsJsonGen();

  /// File path: assets/json/surahs.json
  String get surahs => 'assets/json/surahs.json';

  /// List of all assets
  List<String> get values => [surahs];
}

class Assets {
  Assets._();

  static const $AssetsFontsGen fonts = $AssetsFontsGen();
  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsJsonGen json = $AssetsJsonGen();
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  });

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.low,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
