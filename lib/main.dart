import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quran_broadcast_app/src/app.dart';
import 'package:quran_broadcast_app/src/core/shared/utils/initialize.dart';
import 'package:xr_helper/xr_helper.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    await initialize();

    // Android now uses native scheduling, no need for AndroidAlarmManager
    Log.i('App initialization completed');
  } catch (e) {
    Log.e('Initialization Error: $e');
  }

  runApp(
    const ProviderScope(
      child: BaseApp(),
    ),
  );
}
