import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:just_audio/just_audio.dart';
import 'package:quran_broadcast_app/generated/assets.gen.dart';
import 'package:quran_broadcast_app/src/core/shared/services/connectivity_internet/connectivity_internet.dart';
import 'package:quran_broadcast_app/src/core/shared/widgets/loading/loading_widget.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:quran_broadcast_app/src/features/audio_stream/view/widgets/audio_handler.dart';
import 'package:waveform_flutter/waveform_flutter.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../core/consts/app_constants.dart';
import '../../settings/providers/settings.providers.dart';

// Global variables for persisting state
// final AudioPlayer globalAudioPlayer = AudioPlayer();
final globalAudioPlayer = AudioPlayer();

final MyAudioHandler globalAudioPlayerHandler = MyAudioHandler();
final StreamController<Amplitude> globalAmplitudeController =
    StreamController<Amplitude>.broadcast();
final ValueNotifier<bool> globalIsPlaying = ValueNotifier(false);
final ValueNotifier<bool> globalIsInitialing = ValueNotifier(true);

//? If internet is not available, check from second time
bool hasInitializedAudio = false; // Flag to track initialization

class AudioStreamScreen extends HookConsumerWidget {
  const AudioStreamScreen({super.key});

  Stream<Amplitude> createAudioAmplitudeStream() {
    return Stream.periodic(
      const Duration(milliseconds: 70),
      (count) => Amplitude(
        current: Random().nextDouble() * 100,
        max: 100,
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Enable keep-alive for this widget
    useAutomaticKeepAlive();

    final settingsController = ref.watch(settingsControllerNotifierProvider);

    // Create and manage amplitude subscription using useEffect
    useEffect(() {
      final subscription = createAudioAmplitudeStream().listen((amplitude) {
        if (!globalAmplitudeController.isClosed) {
          globalAmplitudeController.add(amplitude);
        }
      });

      return subscription.cancel;
    }, []);

    return StatefulBuilder(builder: (context, setState) {
      try {
        globalAudioPlayer.playingStream.listen((isPlaying) {
          if (globalIsPlaying.value != isPlaying) {
            setState(() {
              globalIsPlaying.value = isPlaying;
            });
          }
        });
      } catch (e) {
        Log.e('Error listening to audio player: $e');
      }

      Future<void> stopAudio() async {
        try {
          await globalAudioPlayerHandler.stop();

          globalIsPlaying.value = false;

          globalAmplitudeController
              .add(Amplitude(current: 0, max: 100)); // Reset wave

          setState(() {});
        } catch (e) {
          debugPrint('Error stopping audio: $e');
        }
      }

      // final settingsController = ref.watch(settingsControllerNotifierProvider);

      Future<void> preloadAudio() async {
        try {
          final url = settingsController.settings.value.live.url.isEmpty
              ? AppConsts.liveUrl
              : settingsController.settings.value.live.url;

          Log.w('Internet_Audio_Preloaded: $url');

          await globalAudioPlayerHandler.setUrl(url);

          Log.w('Internet_Audio_Preloaded22: $url');
        } catch (e) {
          debugPrint('Error preloading audio: $e');
        }
      }

      Future<void> startAudio() async {
        try {
          // if (Platform.isIOS) {
          //   await preloadAudio();
          // }
          await globalAudioPlayerHandler.play();
        } catch (e) {
          debugPrint('Error starting audio: $e');
          globalIsPlaying.value = false;
        }

        setState(() {});
      }

      return NetworkAwareWidget(
        offlineChild: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(CupertinoIcons.wifi_slash,
                  size: 100, color: Colors.grey),
              AppGaps.gap16,
              Text(
                'لا يوجد اتصال بالإنترنت',
                style: AppTextStyles.subTitle,
              ),
            ],
          ),
        ),
        onlineChild: HookBuilder(builder: (context) {
          final networkStatusService = NetworkStatusService();

          useEffect(() {
            if (Platform.isAndroid) {
              final subscription = networkStatusService
                  .networkStatusController.stream
                  .listen((status) {
                if (status == NetworkStatus.Online) {
                  if (!hasInitializedAudio) {
                    hasInitializedAudio = true;
                  } else {
                    preloadAudio();
                  }
                }
              });

              return subscription.cancel;
            }

            return () {};
          }, []);
          return ListView(
            padding: const EdgeInsets.only(bottom: 10.0),
            children: [
              Assets.images.audioStreamLogo.image(
                height: 220,
                fit: BoxFit.contain,
              ),
              Container(
                height: 100,
                width: double.infinity,
                margin: const EdgeInsets.symmetric(horizontal: 16.0),
                padding: const EdgeInsets.all(12.0),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.grey.shade400.withOpacity(0.8),
                      Colors.grey.shade200.withOpacity(0.7),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: ValueListenableBuilder<bool>(
                  valueListenable: globalIsPlaying,
                  builder: (context, isPlayingValue, child) {
                    return isPlayingValue
                        ? StreamBuilder<Amplitude>(
                            stream: globalAmplitudeController.stream,
                            builder: (context, snapshot) {
                              if (snapshot.hasData) {
                                // final amplitude = snapshot.data!;
                                return AnimatedWaveList(
                                  stream: globalAmplitudeController.stream,
                                  barBuilder: (animation, amplitude) =>
                                      WaveFormBar(
                                    maxHeight: 10,
                                    animation: animation,
                                    amplitude: amplitude,
                                    color: ColorManager.primaryColor,
                                  ),
                                );
                              } else {
                                return const SizedBox
                                    .shrink(); // Placeholder if no data
                              }
                            },
                          )
                        : Assets.images.audioWaves.image(
                            fit: BoxFit.cover,
                            width: double.infinity,
                          );
                  },
                ),
              ),
              AppGaps.gap24,
              ValueListenableBuilder<bool>(
                valueListenable: globalIsPlaying,
                builder: (context, isPlayingValue, child) {
                  return ValueListenableBuilder<bool>(
                    valueListenable: Platform.isAndroid
                        ? globalIsInitialing
                        : ValueNotifier(false),
                    builder: (context, isInitialing, child) {
                      if (isInitialing) {
                        return const Center(child: LoadingWidget());
                      }
                      return CircleAvatar(
                        radius: 30,
                        backgroundColor: isPlayingValue
                            ? ColorManager.secondaryColor
                            : ColorManager.primaryColor,
                        child: IconButton(
                          icon: Icon(
                            isPlayingValue ? Icons.stop : Icons.play_arrow,
                            color: Colors.white,
                            size: 32,
                          ),
                          onPressed: isPlayingValue ? stopAudio : startAudio,
                        ),
                      );
                    },
                  );
                },
              ),
              AppGaps.gap96,
            ],
          );
        }),
      );
    });
  }
}
