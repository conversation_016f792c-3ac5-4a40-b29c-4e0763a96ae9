import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:quran_broadcast_app/src/core/shared/widgets/loading/loading_widget.dart';
import 'package:quran_broadcast_app/src/features/settings/providers/settings.providers.dart';
import 'package:xr_helper/xr_helper.dart';

class AboutUsScreen extends ConsumerWidget {
  const AboutUsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsController = ref.watch(settingsControllerNotifierProvider);

    final aboutUs = settingsController.settings.value.aboutUs;

    return settingsController.isLoading
        ? const Center(
            child: LoadingWidget(),
          )
        : SingleChildScrollView(
            padding: const EdgeInsets.all(AppSpaces.screenPadding),
            child: HtmlWidget(
              aboutUs.content,
              customStylesBuilder: (element) {
                if (element.classes.contains('ql-align-center')) {
                  return {'text-align': 'center'};
                }
                return null;
              },
              onLoadingBuilder: (context, element, loadingProgress) =>
                  const Center(child: LoadingWidget()),
            ),
          );
  }
}

// import 'package:flutter/material.dart';
// import 'package:quran_broadcast_app/src/core/shared/widgets/background_widgets/background_text_body.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// class AboutUsScreen extends StatelessWidget {
//   const AboutUsScreen({super.key});
//
//   @override
//   Widget build(BuildContext context) {
// final aboutUsText = '''
//     return BackgroundTextBody(
//       headerText: 'من نحن',
//       child: ListView(
//         padding: const EdgeInsets.only(
//           bottom: AppSpaces.padding8,
//         ),
//         children: [
//           Text(
//             aboutUsText,
//             textAlign: TextAlign.center,
//             style: AppTextStyles.body,
//           ),
//         ],
//       ),
//     );
//   }
// }
