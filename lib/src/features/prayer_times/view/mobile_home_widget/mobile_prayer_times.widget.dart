import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:quran_broadcast_app/src/features/calendar/providers/calendar.providers.dart';
import 'package:quran_broadcast_app/src/features/home/<USER>/widgets/next_prayer_times.dart';
import 'package:xr_helper/xr_helper.dart';

class MobilePrayerTimesWidget extends HookConsumerWidget {
  const MobilePrayerTimesWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final calendarController =
            ref.watch(calendarControllerNotifierProvider);
        final currentDayData =
            calendarController.calendarByDate(DateTime.now());
        final nextPrayerTime = getNextPrayerTime(currentDayData.prayerTimes);

        // Get screen width dynamically
        final screenWidth = constraints.maxWidth;

        // Maintain a 4x2 aspect ratio
        final widgetHeight = screenWidth * (2 / 4) + 50;

        final List<Map<String, String>> azanTimes = [
          {
            "time": currentDayData.prayerTimes.fajr,
            "title": "الفجر",
            "icon": "moon"
          },
          {
            "time": currentDayData.prayerTimes.sunrise,
            "title": "الشروق",
            "icon": "sun"
          },
          {
            "time": currentDayData.prayerTimes.dhuhr,
            "title": "الظهر",
            "icon": "sun"
          },
          {
            "time": currentDayData.prayerTimes.asr,
            "title": "العصر",
            "icon": "sun"
          },
          {
            "time": currentDayData.prayerTimes.maghrib,
            "title": "المغرب",
            "icon": "moon"
          },
          {
            "time": currentDayData.prayerTimes.isha,
            "title": "العشاء",
            "icon": "moon"
          },
        ];

        final List<Map<String, String>> reversedAzanTimes =
            azanTimes.reversed.toList();

        final nextPrayerTimeHour = nextPrayerTime.time.hour;
        final nextPrayerPmOrAm = nextPrayerTimeHour >= 12 ? 'م' : 'ص';

        final formattedTime = TimeOfDay(
          hour: nextPrayerTimeHour == 12
              ? 12
              : nextPrayerTimeHour > 12
                  ? nextPrayerTimeHour - 12
                  : nextPrayerTimeHour,
          minute: nextPrayerTime.time.minute,
        );

        final formattedMinute = formattedTime.minute.toString().padLeft(2, '0');

        return Container(
          width: screenWidth,
          height: widgetHeight, // Adjust height dynamically
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(16)),
            color: ColorManager.white,
            image: DecorationImage(
              image: AssetImage('assets/images/banner.webp'),
              fit: BoxFit.cover,
            ),
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              Container(
                height: widgetHeight,
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              Positioned.fill(
                child: Column(
                  children: [
                    const Spacer(
                      flex: 2,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: AppSpaces.padding16),
                      child: Row(
                        children: [
                          AppGaps.gap12,
                          Container(
                            width: widgetHeight * 0.39,
                            height: widgetHeight * 0.39,
                            decoration: const BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  FittedBox(
                                    fit: BoxFit.scaleDown,
                                    child: Text(
                                      nextPrayerTime.name,
                                      style: AppTextStyles.whiteLabelLarge
                                          .copyWith(
                                        color: ColorManager.primaryColor,
                                        fontWeight: FontWeight.bold,
                                        fontSize: widgetHeight * 0.08,
                                        fontFamily: 'Cairo',
                                      ),
                                    ),
                                  ),
                                  FittedBox(
                                    fit: BoxFit.scaleDown,
                                    child: RichText(
                                      maxLines: 1,
                                      textDirection: TextDirection.rtl,
                                      text: TextSpan(
                                        children: [
                                          TextSpan(
                                            text:
                                                '${formattedTime.hour}:$formattedMinute',
                                            style: AppTextStyles.whiteLabelLarge
                                                .copyWith(
                                              color: ColorManager.primaryColor,
                                              fontWeight: FontWeight.bold,
                                              fontSize: widgetHeight * 0.08,
                                              fontFamily: 'Cairo',
                                            ),
                                          ),
                                          TextSpan(
                                            text: ' $nextPrayerPmOrAm',
                                            style: AppTextStyles.whiteLabelLarge
                                                .copyWith(
                                              color: ColorManager.primaryColor,
                                              fontWeight: FontWeight.bold,
                                              fontSize: widgetHeight * 0.05,
                                              fontFamily: 'Cairo',
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const Spacer(),
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.12),
                                  spreadRadius: 12,
                                  blurRadius: 14,
                                  offset: const Offset(0, .8),
                                ),
                              ],
                            ),
                            child: Text(
                              'الصلاة القادمة',
                              style: AppTextStyles.whiteSubTitle.copyWith(
                                fontSize: widgetHeight * 0.12,
                                fontFamily: 'Cairo',
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Spacer(),
                    Padding(
                      padding: const EdgeInsets.all(AppSpaces.padding8),
                      child: Row(
                        children: reversedAzanTimes.map((azanTime) {
                          bool isSelected =
                              azanTime['title'] == nextPrayerTime.name;
                          final prayerTime = azanTime['time'] ?? '';

                          final hour = int.parse(prayerTime.split(':')[0]);
                          final pmOrAm = hour >= 12 ? 'م' : 'ص';

                          final formattedTime = TimeOfDay(
                            hour: hour == 12
                                ? 12
                                : hour > 12
                                    ? hour - 12
                                    : hour,
                            minute: int.parse(prayerTime.split(':')[1]),
                          );

                          final formattedMinute =
                              formattedTime.minute.toString().padLeft(2, '0');

                          return Expanded(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: AppSpaces.padding4),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  if (isSelected) AppGaps.gap8,
                                  Icon(
                                    azanTime['icon'] == "sun"
                                        ? Icons.wb_sunny
                                        : Icons.nights_stay,
                                    color: azanTime['icon'] == "sun"
                                        ? ColorManager.secondaryColor
                                        : Colors.white,
                                    size: isSelected
                                        ? widgetHeight * 0.14
                                        : widgetHeight * 0.12,
                                  ),
                                  if (isSelected)
                                    AppGaps.gap4
                                  else
                                    AppGaps.gap8,
                                  Container(
                                    padding: isSelected
                                        ? const EdgeInsets.all(
                                            AppSpaces.padding8)
                                        : EdgeInsets.zero,
                                    decoration: BoxDecoration(
                                      color: isSelected
                                          ? Colors.white
                                          : Colors.transparent,
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Column(
                                      children: [
                                        FittedBox(
                                          fit: BoxFit.scaleDown,
                                          child: Text(
                                            azanTime['title'] ?? '',
                                            style: AppTextStyles.whiteLabelLarge
                                                .copyWith(
                                              fontWeight: FontWeight.bold,
                                              color: isSelected
                                                  ? ColorManager.primaryColor
                                                  : Colors.white,
                                              fontFamily: 'Cairo',
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                        FittedBox(
                                          fit: BoxFit.scaleDown,
                                          child: RichText(
                                            textDirection: TextDirection.rtl,
                                            text: TextSpan(
                                              children: [
                                                TextSpan(
                                                  text:
                                                      '${formattedTime.hour}:$formattedMinute',
                                                  style: AppTextStyles
                                                      .whiteLabelLarge
                                                      .copyWith(
                                                    color: isSelected
                                                        ? ColorManager
                                                            .primaryColor
                                                        : Colors.white,
                                                    fontFamily: 'Cairo',
                                                  ),
                                                ),
                                                TextSpan(
                                                  text: ' $pmOrAm',
                                                  style: AppTextStyles
                                                      .whiteLabelLarge
                                                      .copyWith(
                                                    color: isSelected
                                                        ? ColorManager
                                                            .primaryColor
                                                        : Colors.white,
                                                    fontFamily: 'Cairo',
                                                    fontSize:
                                                        widgetHeight * 0.04,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                    const Spacer(),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
