import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:quran_broadcast_app/src/core/consts/app_constants.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

class NotifyTimesDialogWidget extends HookWidget {
  final String selectedIndex;
  final Function(String) onSelected;

  const NotifyTimesDialogWidget({
    super.key,
    required this.selectedIndex,
    required this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    final selected = useState(selectedIndex);

    return Wrap(
      children: AppConsts.prayerTimeReminders.indexed.map(
        (e) {
          final time = e.$2;

          return HookBuilder(builder: (context) {
            final isSelected = selected.value == time;

            return GestureDetector(
              onTap: () {
                selected.value = time;
                onSelected(time);
              },
              child: Container(
                padding: const EdgeInsets.all(AppSpaces.padding8),
                margin: const EdgeInsets.symmetric(
                  vertical: AppSpaces.padding8 - 2,
                  horizontal: AppSpaces.padding12,
                ),
                decoration: BoxDecoration(
                  color: isSelected
                      ? ColorManager.primaryColor
                      : ColorManager.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppRadius.radius8),
                ),
                child: Text(
                  time,
                  style: TextStyle(
                    color: isSelected
                        ? ColorManager.white
                        : ColorManager.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            );
          });
        },
      ).toList(),
    ).paddingOnly(top: AppSpaces.padding8);
  }
}
