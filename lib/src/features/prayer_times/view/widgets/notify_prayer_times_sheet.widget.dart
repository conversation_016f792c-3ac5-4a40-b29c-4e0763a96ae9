import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:quickalert/quickalert.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:quran_broadcast_app/src/features/calendar/providers/calendar.providers.dart';
import 'package:quran_broadcast_app/src/features/main_screen/view/main.screen.dart';
import 'package:quran_broadcast_app/src/features/notifications/controller/notification_controller.dart';
import 'package:quran_broadcast_app/src/features/prayer_times/view/widgets/notify_times_dialog.widget.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/consts/app_constants.dart';

class NotifyPrayerTimesWidget extends HookConsumerWidget {
  const NotifyPrayerTimesWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final calendarController = ref.watch(calendarControllerNotifierProvider);
    final notificationController =
        ref.watch(notificationControllerProvider(ref));
    final currentDay = calendarController.currentDayCalendar;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.only(
                right: AppSpaces.padding16,
                left: AppSpaces.padding16,
                bottom: AppSpaces.padding16),
            itemBuilder: (context, i) {
              return HookBuilder(builder: (context) {
                final isSelected =
                    useState(notificationController.notificationStates[i]);
                final reminderTime =
                    useState(notificationController.reminderTimes[i]);

                final hour = int.parse(currentDay.prayerTimes
                    .toJson()
                    .values
                    .toList()[i]
                    .split(':')[0]);

                final pmOrAm = hour >= 12 ? 'م' : 'ص';

                final formattedTime = TimeOfDay(
                  hour: hour == 12
                      ? 12
                      : hour > 12
                          ? hour - 12
                          : hour,
                  minute: int.parse(currentDay.prayerTimes
                      .toJson()
                      .values
                      .toList()[i]
                      .split(':')[1]),
                );

                final formattedMinute =
                    formattedTime.minute.toString().padLeft(2, '0');
                final isNextTimeIsShorouk =
                    AppConsts.prayerNames[i] == 'الشروق';

                return Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: ColorManager.primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(AppRadius.radius8),
                      ),
                      child: Text(
                        AppConsts.prayerNames[i],
                        style: AppTextStyles.labelLarge.copyWith(
                          color: ColorManager.primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    AppGaps.gap12,
                    Text(
                      '${formattedTime.hour}:$formattedMinute $pmOrAm',
                      style: AppTextStyles.labelLarge.copyWith(
                        fontSize: 16,
                      ),
                    ),
                    const Spacer(),
                    if (isSelected.value && !isNextTimeIsShorouk) ...[
                      GestureDetector(
                        onTap: () {
                          QuickAlert.show(
                            context: context,
                            confirmBtnColor: ColorManager.primaryColor,
                            widget: NotifyTimesDialogWidget(
                              selectedIndex: reminderTime.value,
                              onSelected: (value) {
                                reminderTime.value = value;
                                notificationController.setReminderTime(
                                    i, value);
                              },
                            ),
                            type: QuickAlertType.info,
                            showCancelBtn: true,
                            title: 'تذكير قبل أذان ${AppConsts.prayerNames[i]}',
                            titleAlignment: TextAlign.right,
                            cancelBtnText: "إلغاء",
                            confirmBtnText: "تأكيد",
                            onConfirmBtnTap: () async {
                              navService.back();
                            },
                          );
                        },
                        child: Text(
                          reminderTime.value.isNotEmpty
                              ? 'تذكير قبل (${reminderTime.value})'
                              : 'تذكير قبل الأذان',
                          style: AppTextStyles.labelLarge.copyWith(
                            fontSize: 12,
                            color: ColorManager.primaryColor,
                            decoration: TextDecoration.underline,
                            decorationColor: ColorManager.primaryColor,
                          ),
                        ),
                      ),
                      AppGaps.gap4,
                    ],
                    IconButton(
                      onPressed: () {
                        isSelected.value = !isSelected.value;
                        notificationController.toggleNotification(i);

                        if (isSelected.value) {
                          notificationController.setReminderTime(i, '');
                          reminderTime.value = '';
                        }
                      },
                      icon: CircleAvatar(
                        backgroundColor: isSelected.value
                            ? ColorManager.primaryColor
                            : ColorManager.blueGrey,
                        child: Icon(
                            isSelected.value
                                ? Icons.notifications_outlined
                                : Icons.notifications_off_outlined,
                            color: Colors.white),
                      ),
                    ),
                  ],
                );
              });
            },
            separatorBuilder: (context, i) {
              return const Divider(
                color: ColorManager.blueGrey,
                height: 2,
              ).paddingSymmetric(vertical: AppSpaces.padding8);
            },
            itemCount: AppConsts.prayerNames.length,
          ),
        ),
        AppGaps.gap12,
        Button(
          label: 'حفظ',
          onPressed: () {
            notificationController.saveSettingsAndScheduleNotifications();

            const MainScreen().navigateReplacement;
          },
        ).paddingSymmetric(horizontal: AppSpaces.padding16),
        // Button(
        //   label: 'حفظ',
        //   onPressed: () {
        //     notificationController.schedulePrayerNotifications();
        //     navService.back();
        //   },
        // ).paddingSymmetric(horizontal: AppSpaces.padding16),
        AppGaps.gap16,
      ],
    );
  }
}
