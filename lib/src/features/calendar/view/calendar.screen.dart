import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:quran_broadcast_app/src/core/consts/app_constants.dart';
import 'package:quran_broadcast_app/src/core/shared/extensions/string_extensions.dart';
import 'package:quran_broadcast_app/src/core/shared/widgets/next_previous_widget/next_previous.widget.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:quran_broadcast_app/src/features/calendar/controllers/calendar.controller.dart';
import 'package:quran_broadcast_app/src/features/calendar/providers/calendar.providers.dart';
import 'package:quran_broadcast_app/src/features/calendar/view/widgets/sliver_app_bar.dart';
import 'package:quran_broadcast_app/src/features/settings/providers/settings.providers.dart';
import 'package:time_picker_spinner_pop_up/time_picker_spinner_pop_up.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../core/shared/widgets/loading/loading_widget.dart';

String formatMonth(DateTime date) => DateFormat('MMMM', 'ar').format(date);

class CalendarScreen extends HookConsumerWidget {
  const CalendarScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentDate = useState(DateTime.now());
    final calendarController = ref.watch(calendarControllerNotifierProvider);
    final settings =
        ref.watch(settingsControllerNotifierProvider).settings.value;

    final calendarData = CalendarController.calendarBySummerTime;

    final scrollController = useScrollController();

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final currentMonthData = calendarData.firstWhereOrNull(
          (calendar) =>
              calendar.gregorianMonthNumber == currentDate.value.month &&
              calendar.gregorianYear == currentDate.value.year,
        );

        if (currentMonthData != null) {
          final currentDayIndex = currentMonthData.days.indexWhere(
            (day) => day.gregorianDate == currentDate.value.formatDateToString,
          );

          if (currentDayIndex != -1 && scrollController.hasClients) {
            scrollController.animateTo(
              currentDayIndex * 50.h,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          }
        }
      });
      return null;
    }, [calendarData, currentDate.value]);

    final currentMonthData =
        calendarController.monthDataByDate(currentDate.value);

    final isLastDayMonth = currentDate.value.formatDateToMonthYearString ==
        settings.lastYear?.substring(0, 7);

    final isFirstDayMonth = currentDate.value.formatDateToMonthYearString ==
        settings.firstYear?.substring(0, 7);

    void updateMonth(int increment) async {
      currentDate.value = DateTime(
        currentDate.value.year,
        currentDate.value.month + increment,
        1,
      );

      final isLocalEmpty = calendarData
              .firstWhereOrNull((data) =>
                  data.gregorianMonthNumber == currentDate.value.month &&
                  data.gregorianYear == currentDate.value.year)
              ?.days
              .isEmpty ??
          true;

      if (isLocalEmpty) {
        await calendarController.getCalendar(
            date: currentDate.value.formatDateToString);
      }
    }

    return Stack(
      children: [
        Column(
          children: [
            AppGaps.gap24,
            NextPreviousWidget(
              isCircleAvatar: false,
              onNext: isLastDayMonth ? null : () => updateMonth(1),
              onPrevious: isFirstDayMonth ? null : () => updateMonth(-1),
              centerWidget: Expanded(
                child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSpaces.padding12,
                    ),
                    child: TimePickerSpinnerPopUp(
                      years: settings.years,
                      mode: CupertinoDatePickerMode.monthYear,
                      initTime: currentDate.value,
                      textStyle: AppTextStyles.labelMedium.copyWith(
                        fontFamily: GoogleFonts.alexandria().fontFamily,
                      ),
                      minTime:
                          DateTime.now().subtract(const Duration(days: 300)),
                      maxTime: DateTime.now().add(const Duration(days: 300)),
                      barrierColor: Colors.black12,
                      minuteInterval: 1,
                      locale: Localizations.localeOf(context),
                      padding: const EdgeInsets.fromLTRB(12, 10, 12, 10),
                      cancelText: 'إلغاء',
                      confirmText: 'تأكيد',
                      pressType: PressType.singlePress,
                      paddingHorizontalOverlay: AppSpaces.padding20,
                      timeFormat: 'MM/yyyy',
                      confirmTextStyle: AppTextStyles.labelMedium,
                      cancelTextStyle: AppTextStyles.labelMedium,
                      timeWidgetBuilder: (dateTime) {
                        if (calendarController.isLoading) {
                          return const LoadingWidget();
                        }

                        final List<TextSpan> monthSpans = [];
                        final List<TextSpan> finalSpans = [];

                        for (int i = 0;
                            i < currentMonthData.hijriMonths.length;
                            i++) {
                          final hijriMonth = currentMonthData.hijriMonths[i];

                          monthSpans.add(TextSpan(
                            text: '${hijriMonth.hijriMonthName} ',
                            style:
                                AppTextStyles.subTitle.copyWith(fontSize: 16),
                          ));

                          monthSpans.add(TextSpan(
                            text: '(${hijriMonth.hijriMonthNumber}) ',
                            style:
                                AppTextStyles.subTitle.copyWith(fontSize: 12),
                          ));

                          // Add dash between months (except the last one)
                          if (i < currentMonthData.hijriMonths.length - 1) {
                            monthSpans.add(TextSpan(
                              text: ' - ',
                              style:
                                  AppTextStyles.subTitle.copyWith(fontSize: 16),
                            ));
                          }

                          // If it's the last month of a group or the end, add the year
                          if (i == currentMonthData.hijriMonths.length - 1 ||
                              hijriMonth.hijriYear !=
                                  currentMonthData
                                      .hijriMonths[i + 1].hijriYear) {
                            monthSpans.add(TextSpan(
                              text: '${hijriMonth.hijriYear}هـ ',
                              style: AppTextStyles.subTitle,
                            ));

                            // Add to final list and clear monthSpans for the next group
                            finalSpans.addAll(monthSpans);
                            monthSpans.clear();
                          }
                        }

                        return Row(
                          children: [
                            Expanded(
                              child: Text.rich(
                                textAlign: TextAlign.center,
                                TextSpan(
                                  children: [
                                    ...finalSpans,
                                    TextSpan(
                                      text:
                                          '\n${currentMonthData.gregorianMonthName} ',
                                      style: AppTextStyles.subTitle
                                          .copyWith(fontSize: 16),
                                    ),
                                    TextSpan(
                                      text:
                                          '(${currentMonthData.gregorianMonthNumber}) ',
                                      style: AppTextStyles.subTitle
                                          .copyWith(fontSize: 12),
                                    ),
                                    TextSpan(
                                      text:
                                          '${currentMonthData.gregorianYear}م',
                                      style: AppTextStyles.subTitle,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            AppGaps.gap4,
                            const CircleAvatar(
                              radius: 15,
                              backgroundColor: ColorManager.secondaryColor,
                              child: Icon(
                                CupertinoIcons.calendar,
                                size: 18,
                              ),
                            ),
                          ],
                        );
                      },
                      onChange: (dateTime) async {
                        currentDate.value = dateTime;

                        final isLocalEmpty = calendarData
                                .firstWhereOrNull((data) =>
                                    data.gregorianMonthNumber ==
                                        currentDate.value.month &&
                                    data.gregorianYear ==
                                        currentDate.value.year)
                                ?.days
                                .isEmpty ??
                            true;

                        if (isLocalEmpty) {
                          await calendarController.getCalendar(
                              date: currentDate.value.formatDateToString);
                        }
                      },
                    )),
              ),
            ),
            AppGaps.gap16,
            // Prayer Times Table
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: CustomScrollView(
                  controller: scrollController,
                  slivers: [
                    SliverPersistentHeader(
                      pinned: true,
                      delegate: SliverAppBarDelegate(
                        minHeight: 35.0,
                        maxHeight: 35.0,
                        child: Container(
                          color: Colors.grey.shade100,
                          child: Table(
                            defaultVerticalAlignment:
                                TableCellVerticalAlignment.middle,
                            border: TableBorder.all(
                              color: Colors.grey.shade300,
                              width: 1.5,
                              borderRadius: BorderRadius.circular(7),
                            ),
                            children: [
                              TableRow(
                                children: [
                                  Center(
                                    child: Padding(
                                      padding: const EdgeInsets.all(
                                          AppSpaces.padding8),
                                      child: Text(
                                        "اليوم",
                                        style:
                                            AppTextStyles.labelSmall.copyWith(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                                  for (final prayer in AppConsts.prayerNames)
                                    Center(
                                      child: Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: FittedBox(
                                          fit: BoxFit.scaleDown,
                                          alignment: Alignment.center,
                                          child: Text(
                                            prayer,
                                            style: AppTextStyles.labelSmall
                                                .copyWith(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (BuildContext context, int index) {
                          final currentDayData = currentMonthData.days[index];

                          // Check if this is the current day
                          final isToday = currentDayData.gregorianDate ==
                              DateTime.now().formatDateToString;

                          final isEven = index % 2 == 0;
                          return Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(7),
                              color: isToday
                                  ? ColorManager.lightPrimaryColor3
                                  : isEven
                                      ? ColorManager.lightPrimaryColor2
                                      : null,
                            ),
                            child: Table(
                              defaultVerticalAlignment:
                                  TableCellVerticalAlignment.middle,
                              border: TableBorder.symmetric(
                                inside: BorderSide(
                                  color: Colors.grey.shade300,
                                  width: 1,
                                ),
                              ),
                              children: [
                                TableRow(
                                  children: [
                                    Center(
                                      child: Padding(
                                        padding: const EdgeInsets.all(
                                            AppSpaces.padding8),
                                        child: Column(
                                          children: [
                                            FittedBox(
                                              fit: BoxFit.scaleDown,
                                              child: Text(
                                                currentDayData
                                                    .gregorianDate.dayName,
                                                style: AppTextStyles.labelSmall
                                                    .copyWith(
                                                  color: isToday
                                                      ? Colors.white
                                                      : Colors.black,
                                                ),
                                              ),
                                            ),
                                            FittedBox(
                                              fit: BoxFit.scaleDown,
                                              child: Text(
                                                '${currentMonthData.gregorianMonthNumber}/${currentDayData.gregorianDayNumber} م',
                                                style: AppTextStyles.labelSmall
                                                    .copyWith(
                                                  color: isToday
                                                      ? Colors.white
                                                      : Colors.black,
                                                ),
                                              ),
                                            ),
                                            FittedBox(
                                              fit: BoxFit.scaleDown,
                                              child: Text(
                                                '${currentDayData.hijriMonthNumber}/${currentDayData.hijriDayNumber} هـ',
                                                style: AppTextStyles.labelSmall
                                                    .copyWith(
                                                  color: isToday
                                                      ? Colors.white
                                                      : Colors.black,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    for (final prayerTime in currentDayData
                                        .prayerTimes
                                        .toJson()
                                        .values)
                                      Builder(builder: (context) {
                                        final hour = int.parse(
                                            prayerTime.toString().isEmpty
                                                ? '0'
                                                : prayerTime.split(':')[0]);
                                        final pmOrAm = hour >= 12 ? 'م' : 'ص';

                                        final formattedTime = TimeOfDay(
                                          hour: hour == 12
                                              ? 12
                                              : hour > 12
                                                  ? hour - 12
                                                  : hour,
                                          minute: int.parse(
                                              prayerTime.toString().isEmpty
                                                  ? '0'
                                                  : prayerTime.split(':')[1]),
                                        );

                                        final formattedMinute = formattedTime
                                            .minute
                                            .toString()
                                            .padLeft(2, '0');

                                        return FittedBox(
                                          fit: BoxFit.scaleDown,
                                          child: Text(
                                            prayerTime == null ||
                                                    prayerTime
                                                        .toString()
                                                        .isEmpty
                                                ? 'لا يوجد'
                                                : '${formattedTime.hour}:$formattedMinute $pmOrAm',
                                            style: AppTextStyles.labelSmall
                                                .copyWith(
                                              color: isToday
                                                  ? Colors.white
                                                  : Colors.black,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        );
                                      })
                                  ],
                                ),
                              ],
                            ),
                          );
                        },
                        childCount: currentMonthData.days.length,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ).paddingSymmetric(horizontal: AppSpaces.screenPadding),
        Positioned(
          top: 0,
          right: 0,
          left: 0,
          child: Material(
            color: Colors.transparent,
            child: Text(
              'حسب توقيت المسجد الأقصى',
              textAlign: TextAlign.center,
              style: AppTextStyles.labelSmall.copyWith(fontSize: 10),
            ),
          ),
        ),
        // Positioned(
        //   top: 15,
        //   left: 40,
        //   child: IconButton(
        //     onPressed: () {},
        //     icon: const CircleAvatar(
        //       backgroundColor: ColorManager.secondaryColor,
        //       child: Icon(CupertinoIcons.calendar),
        //     ),
        //   ),
        // ),
      ],
    );
  }
}
