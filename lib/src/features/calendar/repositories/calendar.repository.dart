import 'package:quran_broadcast_app/src/core/consts/network/api_endpoints.dart';
import 'package:quran_broadcast_app/src/features/calendar/models/calendar_model.dart';
import 'package:xr_helper/xr_helper.dart';

class CalendarRepository with BaseRepository {
  final BaseApiServices networkApiService;

  CalendarRepository({
    required this.networkApiService,
  });

  Future<List<CalendarModel>> getCalendar({
    required String date,
  }) async {
    return baseFunction(
      () async {
        final params = '?date=$date&count=1';
        final url = ApiEndpoints.calendar + params;

        final response = await networkApiService.getResponse(url);

        final data = response['data'] as List;

        final calendar = List<CalendarModel>.from(
          data.map((x) => CalendarModel.fromJson(x)),
        );

        return calendar;
      },
    );
  }
}
