import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:quran_broadcast_app/src/core/shared/providers/network_api_service_provider.dart';
import 'package:quran_broadcast_app/src/features/calendar/controllers/calendar.controller.dart';
import 'package:quran_broadcast_app/src/features/calendar/repositories/calendar.repository.dart';

// * Calendar Repo Provider ========================================
final calendarRepoProvider = Provider<CalendarRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);
  return CalendarRepository(networkApiService: networkApiService);
});

// * Calendar Change Notifier Provider ========================================
final calendarControllerNotifierProvider =
    ChangeNotifierProvider<CalendarController>(
  (ref) {
    final calendarRepo = ref.watch(calendarRepoProvider);
    return CalendarController(calendarRepo: calendarRepo);
  },
);

// * Calendar Provider ========================================
final calendarControllerProvider = Provider<CalendarController>(
  (ref) {
    final calendarRepo = ref.watch(calendarRepoProvider);
    return CalendarController(calendarRepo: calendarRepo);
  },
);
