import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:quran_broadcast_app/src/features/calendar/models/calendar_model.dart';
import 'package:quran_broadcast_app/src/features/calendar/repositories/calendar.repository.dart';
import 'package:xr_helper/xr_helper.dart';

class CalendarController extends BaseVM {
  final CalendarRepository calendarRepo;

  CalendarController({
    required this.calendarRepo,
  });

  static ValueNotifier<List<CalendarModel>> calendar = ValueNotifier([]);

  static List<CalendarModel> get calendarBySummerTime {
    final isSummerTime =
        GetStorageService.getData(key: LocalKeys.summerTime) ?? false;

    return calendar.value.map((calendarModel) {
      final updatedDays = calendarModel.days.map((day) {
        if (isSummerTime) {
          return DayModel(
            gregorianDate: day.gregorianDate,
            gregorianDayNumber: day.gregorianDayNumber,
            hijriDate: day.hijriDate,
            hijriDayNumber: day.hijriDayNumber,
            wisdom: day.wisdom,
            prayerTimes: PrayerTimeModel(
              fajr: _addOneHour(day.prayerTimes.fajr),
              sunrise: _addOneHour(day.prayerTimes.sunrise),
              dhuhr: _addOneHour(day.prayerTimes.dhuhr),
              asr: _addOneHour(day.prayerTimes.asr),
              maghrib: _addOneHour(day.prayerTimes.maghrib),
              isha: _addOneHour(day.prayerTimes.isha),
              calendarDate: day.prayerTimes.calendarDate,
            ),
          );
        } else {
          return day;
        }
      }).toList();

      return CalendarModel(
        gregorianMonthName: calendarModel.gregorianMonthName,
        gregorianMonthNumber: calendarModel.gregorianMonthNumber,
        gregorianYear: calendarModel.gregorianYear,
        hijriMonths: calendarModel.hijriMonths,
        days: updatedDays,
      );
    }).toList();
  }

  static String _addOneHour(String time) {
    if (time.isEmpty) return '';
    final parts = time.split(':');
    final hour = int.parse(parts[0]) + 1;
    final minute = parts[1];
    return '$hour:$minute';
  }

  DayModel get currentDayCalendar =>
      calendarBySummerTime.expand((calendar) => calendar.days).firstWhereOrNull(
            (day) =>
        day.gregorianDate == DateTime
            .now()
            .formatDateToString,
      ) ??
          DayModel.empty();

  DayModel calendarByDate(DateTime date) =>
      calendarBySummerTime.expand((calendar) => calendar.days).firstWhereOrNull(
            (day) => day.gregorianDate == date.formatDateToString,
      ) ??
          DayModel.empty();

  DayModel calendarByDateWisdomScreen(DateTime date) =>
      calendar.value.expand((calendar) => calendar.days).firstWhereOrNull(
            (day) => day.gregorianDate == date.formatDateToString,
      ) ??
          DayModel.empty();

  CalendarModel monthDataByDate(DateTime date) =>
      calendarBySummerTime.firstWhereOrNull(
            (data) =>
        data.gregorianMonthNumber == date.month &&
            data.gregorianYear == date.year,
      ) ??
          CalendarModel.empty();

  HijriMonth hijriMonthByDate(DateTime date) =>
      monthDataByDate(date).hijriMonths.firstWhereOrNull(
            (element) =>
        element.hijriMonthNumber.toString() ==
            calendarByDate(date).hijriMonthNumber.toString(),
      ) ??
          HijriMonth.empty();

  final ValueNotifier<DateTime> currentDateNotifier =
  ValueNotifier(DateTime.now());

  void changeDateByDateTime(DateTime date) {
    baseFunction(
          () async {
        currentDateNotifier.value = date;
      },
    );
  }

  void changeDate(int increment) {
    baseFunction(
          () async {
        currentDateNotifier.value = DateTime(
          currentDateNotifier.value.year,
          currentDateNotifier.value.month,
          currentDateNotifier.value.day + increment,
        );
      },
    );
  }

  Future<List<CalendarModel>> getCalendar({
    required String date,
    bool overrideLocalData = false,
  }) async {
    return await baseFunction(
          () async {
        final haveInternet = await InternetConnection().hasInternetAccess;

        if (haveInternet) {
          final newCalendarData = await calendarRepo.getCalendar(date: date);
          if (overrideLocalData) {
            calendar.value = newCalendarData;
          } else {
            mergeCalendarData(newCalendarData);
          }
          saveCalendar(calendar: calendar.value);
        } else {
          calendar.value = await getCalendarFromLocal();
        }

        return calendar.value;
      },
    );
  }

  void mergeCalendarData(List<CalendarModel> newCalendarData) {
    final existingData = calendar.value;
    final mergedData = <CalendarModel>[];

    // Add all existing data to mergedData
    mergedData.addAll(existingData);

    for (var newCalendar in newCalendarData) {
      final existingCalendar = existingData.firstWhereOrNull(
            (calendar) =>
        calendar.gregorianMonthNumber == newCalendar.gregorianMonthNumber &&
            calendar.gregorianYear == newCalendar.gregorianYear,
      );

      if (existingCalendar == null) {
        mergedData.add(newCalendar);
      }
    }

    mergedData.sort((a, b) {
      final aDate = DateTime(a.gregorianYear, a.gregorianMonthNumber);
      final bDate = DateTime(b.gregorianYear, b.gregorianMonthNumber);

      return aDate.compareTo(bDate);
    });

    calendar.value = mergedData;
  }

  Future<void> saveCalendar({
    required List<CalendarModel> calendar,
  }) async {
    return await baseFunction(
          () async {
        GetStorageService.setData(
          key: LocalKeys.calendar,
          value: calendar.map((e) => e.toJson()).toList(),
        );
      },
    );
  }

  Future<List<CalendarModel>> getCalendarFromLocal() async {
    return await baseFunction(
          () async {
        final calendarData =
        await GetStorageService.getData(key: LocalKeys.calendar);

        if (calendarData != null) {
          final calendarList = (calendarData as List)
              .map((item) => CalendarModel.fromJson(item))
              .toList();

          calendarList.sort((a, b) {
            final aDate = DateTime(a.gregorianYear, a.gregorianMonthNumber);
            final bDate = DateTime(b.gregorianYear, b.gregorianMonthNumber);

            return aDate.compareTo(bDate);
          });

          return calendarList;
        }

        return <CalendarModel>[];
      },
    );
  }
}
