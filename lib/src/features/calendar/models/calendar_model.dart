class HijriMonth {
  final String hijriMonthName;
  final int hijriMonthNumber;
  final int hijriYear;

  HijriMonth({
    required this.hijriMonthName,
    required this.hijriMonthNumber,
    required this.hijriYear,
  });

  factory HijriMonth.fromJson(Map<String, dynamic> json) {
    return HijriMonth(
      hijriMonthName: json['hijri_month_name'],
      hijriMonthNumber: json['hijri_month_number'],
      hijriYear: json['hijri_year'],
    );
  }

  factory HijriMonth.empty() {
    return HijriMonth(
      hijriMonthName: '',
      hijriMonthNumber: 0,
      hijriYear: 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'hijri_month_name': hijriMonthName,
      'hijri_month_number': hijriMonthNumber,
      'hijri_year': hijriYear,
    };
  }
}

class DayModel {
  final String gregorianDate;
  final int gregorianDayNumber;
  final String hijriDate;
  final int hijriDayNumber;
  final String wisdom;
  final PrayerTimeModel prayerTimes;

  String get hijriMonthNumber {
    if (hijriDate.split('-').length < 2) {
      return '';
    }
    return hijriDate.split('-')[1];
  }

  String get hijriYearNumber {
    if (hijriDate.split('-').length < 3) {
      return '';
    }
    return hijriDate.split('-')[2];
  }

  //gregorianMonthNumber
  String get gregorianMonthNumber {
    if (gregorianDate.split('-').length < 2) {
      return '';
    }
    return gregorianDate.split('-')[1];
  }

  String get gregorianYearNumber {
    if (gregorianDate.split('-').length < 3) {
      return '';
    }
    return gregorianDate.split('-')[0];
  }

  DayModel({
    required this.gregorianDate,
    required this.gregorianDayNumber,
    required this.hijriDate,
    required this.hijriDayNumber,
    required this.wisdom,
    required this.prayerTimes,
  });

  factory DayModel.fromJson(Map<String, dynamic> json) {
    return DayModel(
      gregorianDate: json['gregorian_date'],
      gregorianDayNumber: json['gregorian_day_number'],
      hijriDate: json['hijri_date'],
      hijriDayNumber: json['hijri_day_number'],
      wisdom: json['wisdom'],
      prayerTimes: PrayerTimeModel.fromJson(json['prayer_times'] ?? {}),
    );
  }

  factory DayModel.empty() {
    return DayModel(
      gregorianDate: '',
      gregorianDayNumber: 0,
      hijriDate: '',
      hijriDayNumber: 0,
      wisdom: '',
      prayerTimes: PrayerTimeModel.empty(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'gregorian_date': gregorianDate,
      'gregorian_day_number': gregorianDayNumber,
      'hijri_date': hijriDate,
      'hijri_day_number': hijriDayNumber,
      'wisdom': wisdom,
      'prayer_times': prayerTimes.toJson(),
    };
  }
}

class CalendarModel {
  final String gregorianMonthName;
  final int gregorianMonthNumber;
  final int gregorianYear;
  final List<HijriMonth> hijriMonths;
  final List<DayModel> days;

  CalendarModel({
    required this.gregorianMonthName,
    required this.gregorianMonthNumber,
    required this.gregorianYear,
    required this.hijriMonths,
    required this.days,
  });

  factory CalendarModel.fromJson(Map<String, dynamic> json) {
    return CalendarModel(
      gregorianMonthName: json['gregorian_month_name'],
      gregorianMonthNumber: json['gregorian_month_number'],
      gregorianYear: json['gregorian_year'],
      hijriMonths: (json['hijri_months'] as List)
          .map((item) => HijriMonth.fromJson(item))
          .toList(),
      days: json['days'] == null
          ? []
          : (json['days'] as List)
              .map((item) => DayModel.fromJson(item))
              .toList(),
    );
  }

  factory CalendarModel.empty() {
    return CalendarModel(
      gregorianMonthName: '',
      gregorianMonthNumber: 0,
      gregorianYear: 0,
      hijriMonths: [],
      days: [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'gregorian_month_name': gregorianMonthName,
      'gregorian_month_number': gregorianMonthNumber,
      'gregorian_year': gregorianYear,
      'hijri_months': hijriMonths.map((e) => e.toJson()).toList(),
      'days': days.map((e) => e.toJson()).toList(),
    };
  }

  Map<String, dynamic> toLogJson() {
    return {
      'gregorian_month_name': gregorianMonthName,
      'gregorian_month_number': gregorianMonthNumber,
      'gregorian_year': gregorianYear,
      'hijri_months': hijriMonths.map((e) => e.toJson()).toList(),
    };
  }
}

class PrayerTimeModel {
  final String fajr;
  final String sunrise;
  final String dhuhr;
  final String asr;
  final String maghrib;
  final String isha;
  final String calendarDate;

  const PrayerTimeModel({
    this.fajr = '',
    this.sunrise = '',
    this.dhuhr = '',
    this.asr = '',
    this.maghrib = '',
    this.isha = '',
    this.calendarDate = '',
  });

  factory PrayerTimeModel.fromJson(Map<String, dynamic> json) {
    return PrayerTimeModel(
      fajr: json['fajr'] ?? '',
      sunrise: json['sunrise'] ?? '',
      dhuhr: json['dhuhr'] ?? '',
      asr: json['asr'] ?? '',
      maghrib: json['maghrib'] ?? '',
      isha: json['isha'] ?? '',
      calendarDate: json['calendar_date'] ?? '',
    );
  }

  static PrayerTimeModel empty() {
    return const PrayerTimeModel(
      fajr: '',
      sunrise: '',
      dhuhr: '',
      asr: '',
      maghrib: '',
      isha: '',
      calendarDate: '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'fajr': fajr,
      'sunrise': sunrise,
      'dhuhr': dhuhr,
      'asr': asr,
      'maghrib': maghrib,
      'isha': isha,
    };
  }
}
