enum SearchByFieldType {
  surah,
  verse,
  page,
  juz,
  hizb,
}
//    final List<String> searchByList = [
//       'بالسورة',
//       'بالآية',
//       'بالصفحة',
//       'بالجزء',
//       'بالحزب',
//     ];

extension SearchByFieldTypeExtension on SearchByFieldType {
  String get value {
    switch (this) {
      case SearchByFieldType.surah:
        return 'بالسورة';
      case SearchByFieldType.verse:
        return 'بالآية';
      case SearchByFieldType.page:
        return 'بالصفحة';
      case SearchByFieldType.juz:
        return 'بالجزء';
      case SearchByFieldType.hizb:
        return 'بالحزب';
    }
  }
}
