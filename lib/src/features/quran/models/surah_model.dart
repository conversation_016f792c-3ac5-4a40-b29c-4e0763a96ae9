class SurahModel {
  // final int id;
  // final String revelationPlace;
  // final int revelationOrder;
  // final String name;
  // final String arabicName;
  // final int versesCount;
  //
  // const Surah({
  //   required this.arabicName,
  //   required this.id,
  //   required this.name,
  //   required this.revelationOrder,
  //   required this.revelationPlace,
  //   required this.versesCount,
  // });
  //          suraName: suraName,
  //           suraNameEnglishTranslated: suraNameEnglishTranslated,
  //           suraNumber: suraNumber,
  //           suraNumberInQuran: suraNumberInQuran,
  //           ayahCount: ayahCount,
  //           revelationType: filteredData[index]["revelationType"],
  final String suraArName;
  final String suraEnName;
  final String suraTranslatedEnName;
  final int? suraNumber;
  final int? suraNumberInQuran;
  final int? ayahCount;
  final String revelationType;
  final int? juz;
  final int? hizb;

  SurahModel({
    this.suraArName = '',
    this.suraEnName = '',
    this.suraTranslatedEnName = '',
    this.suraNumber,
    this.suraNumberInQuran,
    this.ayahCount,
    this.revelationType = '',
    this.juz,
    this.hizb,
  });

  factory SurahModel.fromMap(Map<String, dynamic> json) {
    return SurahModel(
      suraArName: json['name'] ?? '',
      suraEnName: json['englishName'] ?? '',
      suraTranslatedEnName: json['englishNameTranslation'] ?? '',
      suraNumber: json['number'],
      revelationType: json['revelationType'] ?? '',
      juz: json['juz'],
      hizb: json['hizb'],
    );
  }

  SurahModel copyWith({
    String? suraArName,
    String? suraEnName,
    String? suraTranslatedEnName,
    int? suraNumber,
    int? suraNumberInQuran,
    int? ayahCount,
    String? revelationType,
    int? juz,
    int? hizb,
  }) {
    return SurahModel(
      suraArName: suraArName ?? this.suraArName,
      suraEnName: suraEnName ?? this.suraEnName,
      suraTranslatedEnName: suraTranslatedEnName ?? this.suraTranslatedEnName,
      suraNumber: suraNumber ?? this.suraNumber,
      ayahCount: ayahCount ?? this.ayahCount,
      revelationType: revelationType ?? this.revelationType,
      suraNumberInQuran: suraNumberInQuran ?? this.suraNumberInQuran,
      juz: juz ?? this.juz,
      hizb: hizb ?? this.hizb,
    );
  }
}

// class Surah {
//   int? number;
//   int? sequence;
//   String? tafsir;
//   int? numberOfVerses;
//   Revelation? revelation;
//   NameSurah? name;
//
//   Surah({
//     this.number,
//     this.sequence,
//     this.tafsir,
//     this.numberOfVerses,
//     this.name,
//   });
//
//   factory Surah.fromJson(Map<String, dynamic> json) {
//     var surah = Surah();
//     surah.number = json['number'];
//     surah.name = NameSurah.fromJson(json['name']);
//     surah.sequence = json['sequence'];
//     surah.numberOfVerses = json['numberOfVerses'];
//     surah.tafsir = json['tafsir']['id'];
//     surah.revelation = Revelation.fromJson(json['revelation']);
//     return surah;
//   }
// }
//
// class NameSurah {
//   String? arab;
//   String? id;
//   String? en;
//   String? translationEn;
//   String? translationId;
//   NameSurah(
//       {this.arab, this.id, this.en, this.translationEn, this.translationId});
//
//   factory NameSurah.fromJson(Map<String, dynamic> json) {
//     return NameSurah(
//       arab: json['short'],
//       en: json['transliteration']['en'],
//       id: json['transliteration']['id'],
//       translationEn: json['translation']['en'],
//       translationId: json['translation']['id'],
//     );
//   }
// }
//
// class Revelation {
//   String? arab;
//   String? en;
//   String? id;
//
//   Revelation({this.arab, this.id, this.en});
//
//   factory Revelation.fromJson(Map<String, dynamic> json) {
//     return Revelation(
//       arab: json['arab'],
//       en: json['en'],
//       id: json['id'],
//     );
//   }
// }
