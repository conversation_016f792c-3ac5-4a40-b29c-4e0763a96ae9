import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:quran_broadcast_app/src/features/quran/view/surah_details_screen/surah_details.screen.dart';
import 'package:xr_helper/xr_helper.dart';

class NavigateToBookmarkFloatingButtonWidget extends HookWidget {
  const NavigateToBookmarkFloatingButtonWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: AppSpaces.padding8,
      ),
      child: FloatingActionButton.extended(
        onPressed: () async {
          final pageNumber =
              GetStorageService.getData(key: LocalKeys.favoritePage);
          final favoriteVersePage =
              GetStorageService.getData(key: LocalKeys.favoriteVersePage);
          final verseNumber =
              GetStorageService.getData(key: LocalKeys.favoriteVerse);

          log('Page: $pageNumber, Verse: $verseNumber & FavoriteVersePage: $favoriteVersePage');
          if (pageNumber != null &&
              favoriteVersePage != null &&
              verseNumber != null) {
            await _showNavigationDialog(
              context,
              page: pageNumber,
              versePage: favoriteVersePage,
              verse: verseNumber,
            );
          } else if (pageNumber != null) {
            _navigateToPage(context, pageNumber);
          } else if (favoriteVersePage != null && verseNumber != null) {
            _navigateToVerse(context, favoriteVersePage, verseNumber);
          } else {
            Fluttertoast.showToast(
              msg: 'لم يتم تحديد علامة بعد !',
              backgroundColor: ColorManager.errorColor,
            );
          }
        },
        label: const Text('الذهاب إلى العلامة',
            style: TextStyle(
              fontFamily: 'Alex',
            )),
        icon: const Icon(Icons.bookmark_outline),
        backgroundColor: ColorManager.primaryColor,
      ),
    );
  }

  Future<void> _showNavigationDialog(
    BuildContext context, {
    required int page,
    required int versePage,
    required int verse,
  }) async {
    QuickAlert.show(
      context: context,
      confirmBtnColor: ColorManager.primaryColor,
      widget: Column(
        children: [
          Button(
            color: ColorManager.primaryColor,
            label: 'الصفحة',
            onPressed: () {
              context.back();
              _navigateToPage(context, page);
            },
          ),
          AppGaps.gap12,
          Button(
            color: ColorManager.secondaryColor,
            label: 'الآية',
            onPressed: () {
              context.back();
              _navigateToVerse(context, versePage, verse);
            },
          ),
        ],
      ),
      type: QuickAlertType.info,
      showCancelBtn: false,
      showConfirmBtn: false,
      title: '',
    );
  }

  void _navigateToPage(BuildContext context, int page) {
    SurahDetailsScreen(
      pageNumber: page,
      highlightVerse: '',
    ).navigate;
  }

  void _navigateToVerse(BuildContext context, int page, int verse) {
    SurahDetailsScreen(
      pageNumber: page,
      highlightVerse: verse.toString(),
    ).navigate;
  }
}
