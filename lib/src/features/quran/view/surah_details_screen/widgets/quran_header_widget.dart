import 'package:flutter/material.dart';
import 'package:quran/quran.dart';

class QuranHeaderWidget extends StatelessWidget {
  final dynamic e;

  const QuranHeaderWidget({
    super.key,
    required this.e,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 50,
      child: Stack(
        children: [
          Center(
            child: Image.asset(
              "assets/images/quran_header.png",
              width: MediaQuery.of(context).size.width,
              height: 50,
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15.7, vertical: 7),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Text(
                  textAlign: TextAlign.center,
                  "اياتها\n${getVerseCount(e["surah"])}",
                  style: const TextStyle(
                      fontSize: 5, fontFamily: "UthmanicHafs13"),
                ),
                Center(
                    child: RichText(
                        text: TextSpan(
                  text: e["surah"].toString(),

                  // textAlign: TextAlign.center,
                  style: const TextStyle(
                      fontFamily: "arsura", fontSize: 22, color: Colors.black),
                ))),
                Text(
                  "ترتيبها\n${e["surah"]}",
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                      fontSize: 5, fontFamily: "UthmanicHafs13"),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
