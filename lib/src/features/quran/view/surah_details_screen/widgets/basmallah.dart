import 'package:flutter/material.dart';

class BasmallahTextWidget extends StatelessWidget {
  final int index;

  const BasmallahTextWidget({super.key, required this.index});

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    return SizedBox(
      width: screenSize.width,
      child: Padding(
        padding: EdgeInsets.only(
            left: (screenSize.width * .2),
            right: (screenSize.width * .2),
            top: 8,
            bottom: 2),
        child: Image.asset(
          "assets/images/basmala.png",
          color: Colors.black,
          width: MediaQuery.of(context).size.width * .4,
        ),
      ),
    );
  }
}

// import 'dart:async';
//
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/gestures.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:just_audio/just_audio.dart';
// import 'package:quran/quran.dart';
// import 'package:quran_broadcast_app/src/core/shared/widgets/background_widgets/background_body.dart';
// import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
// import 'package:quran_broadcast_app/src/features/quran/view/surah_details_screen/widgets/basmallah.dart';
// import 'package:quran_broadcast_app/src/features/quran/view/surah_details_screen/widgets/quran_header_widget.dart';
// import 'package:wakelock_plus/wakelock_plus.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// class QuranViewPage extends HookWidget {
//   final int pageNumber;
//   final dynamic jsonData;
//   final bool shouldHighlightText;
//   final dynamic highlightVerse;
//
//   const QuranViewPage({
//     super.key,
//     required this.pageNumber,
//     required this.jsonData,
//     required this.shouldHighlightText,
//     required this.highlightVerse,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     final index = useState(pageNumber);
//     final pageController = usePageController(initialPage: pageNumber);
//     final isFavorite = useState(false);
//     final selectedSpan = useState("");
//     final highlightVerseState = useState(highlightVerse);
//     final shouldHighlightState = useState(shouldHighlightText);
//
//     useEffect(() {
//       SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
//       SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
//       WakelockPlus.enable();
//
//       if (shouldHighlightText) {
//         final timer = Timer.periodic(
//           const Duration(milliseconds: 400),
//           (timer) {
//             shouldHighlightState.value = !shouldHighlightState.value;
//             if (timer.tick == 4) {
//               highlightVerseState.value = "";
//               shouldHighlightState.value = false;
//               timer.cancel();
//             }
//           },
//         );
//       }
//
//       return () {
//         WakelockPlus.disable();
//         SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
//         _currentPopup?.remove();
//         _currentPopup = null;
//         selectedSpan.value = "";
//       };
//     }, []);
//
//     return Scaffold(
//       backgroundColor: Colors.white,
//       body: BackgroundBody(
//         child: PageView.builder(
//           reverse: true,
//           scrollDirection: Axis.horizontal,
//           onPageChanged: (newIndex) {
//             index.value = newIndex;
//             selectedSpan.value = "";
//           },
//           controller: pageController,
//           itemCount: 605,
//           // Total number of pages (e.g., 604 Quran pages + 1 cover)
//           itemBuilder: (context, currentIndex) {
//             if (currentIndex == 0) {
//               return const CoverPage();
//             }
//             return QuranPage(
//               index: currentIndex,
//               jsonData: jsonData,
//               selectedSpan: selectedSpan,
//               isFavorite: isFavorite,
//             );
//           },
//         ),
//       ),
//     );
//   }
// }
//
// class CoverPage extends StatelessWidget {
//   const CoverPage({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       color: const Color(0xffFFFCE7),
//       child: Image.asset(
//         "assets/images/logo.png",
//         fit: BoxFit.fill,
//       ),
//     );
//   }
// }
//
// class QuranPage extends StatelessWidget {
//   final int index;
//   final dynamic jsonData;
//   final ValueNotifier<String> selectedSpan;
//   final ValueNotifier<bool> isFavorite;
//
//   const QuranPage({
//     super.key,
//     required this.index,
//     required this.jsonData,
//     required this.selectedSpan,
//     required this.isFavorite,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     return SafeArea(
//       child: Padding(
//         padding: const EdgeInsets.symmetric(horizontal: AppSpaces.padding8),
//         child: Column(
//           children: [
//             AppGaps.gap12,
//             HeaderSection(
//               index: index,
//               jsonData: jsonData,
//               isFavorite: isFavorite,
//             ),
//             const Spacer(),
//             RichTextSection(
//               index: index,
//               jsonData: jsonData,
//               selectedSpan: selectedSpan,
//             ),
//             const Spacer(),
//           ],
//         ),
//       ),
//     );
//   }
// }
//

//
// class RichTextSection extends StatelessWidget {
//   final int index;
//   final dynamic jsonData;
//   final ValueNotifier<String> selectedSpan;
//
//   const RichTextSection({
//     super.key,
//     required this.index,
//     required this.jsonData,
//     required this.selectedSpan,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     final height = (index == 1 || index == 2) ? 2.h : 1.5.h;
//     double getFontSize(int index) {
//       if (index == 1 || index == 2) {
//         return 20.sp;
//       } else if (index == 145 || index == 201) {
//         return (index == 532 || index == 533) ? 22.5.sp : 22.4.sp;
//       } else {
//         return 18.75.sp;
//       }
//     }
//
//     return Stack(
//       alignment: Alignment.bottomCenter,
//       children: [
//         Container(
//           // padding: const EdgeInsets.symmetric(
//           //   horizontal: AppSpaces.padding20 + 18,
//           //   vertical: AppSpaces.padding20 + 40,
//           // ),
//           decoration: const BoxDecoration(
//             image: DecorationImage(
//               image: AssetImage('assets/images/quran_background.png'),
//               fit: BoxFit.fill,
//             ),
//           ),
//           child: GestureDetector(
//             onTap: () {
//               _currentPopup?.remove();
//               _currentPopup = null;
//               selectedSpan.value = "";
//             },
//             child: Directionality(
//               textDirection: TextDirection.rtl,
//               child: SizedBox(
//                 width: double.infinity,
//                 child: RichText(
//                   textDirection: TextDirection.rtl,
//                   textAlign: TextAlign.center,
//                   softWrap: true,
//                   locale: const Locale("ar"),
//                   text: TextSpan(
//                     style: TextStyle(
//                       color: Colors.black,
//                       fontSize: 23.sp,
//                     ),
//                     children: getPageData(index).expand((e) {
//                       List<InlineSpan> spans = [];
//                       for (var i = e["start"]; i <= e["end"]; i++) {
//                         if (i == 1) {
//                           spans.add(WidgetSpan(
//                             child: HeaderWidget(
//                               e: e,
//                               jsonData: jsonData,
//                             ),
//                           ));
//
//                           if (index != 187 && index != 1) {
//                             spans.add(const WidgetSpan(
//                               child: BasmallahTextWidget(index: 0),
//                             ));
//                           }
//
//                           if (index == 187) {
//                             spans.add(WidgetSpan(
//                               child: SizedBox(height: 10.h),
//                             ));
//                           }
//                         }
//
//                         spans.add(TextSpan(
//                           recognizer: LongPressGestureRecognizer()
//                             ..onLongPressStart = (details) {
//                               _showPopupMenu(context, details.globalPosition,
//                                   e["surah"], i, selectedSpan);
//                               selectedSpan.value = " ${e["surah"]}$i";
//                             },
//                           text: i == e["start"]
//                               ? "${getVerseQCF(e["surah"], i).substring(0, 1)}\u200A${getVerseQCF(e["surah"], i).substring(1)}"
//                               : '${getVerseQCF(e["surah"], i)} ',
//                           style: TextStyle(
//                             color: selectedSpan.value == " ${e["surah"]}$i"
//                                 ? ColorManager.primaryColor
//                                 : Colors.black,
//                             // height: height,
//                             height: (index == 1 || index == 2) ? 2.h : 1.95.h,
//                             letterSpacing: 0.w,
//                             wordSpacing: 0,
//                             // 1.65.h,
//                             fontFamily:
//                                 "QCF_P${index.toString().padLeft(3, "0")}",
//                             fontSize: index == 1 || index == 2
//                                 ? 28.sp
//                                 : index == 145 || index == 201
//                                     ? index == 532 || index == 533
//                                         ? 22.5.sp
//                                         : 22.4.sp
//                                     : 23.1.sp,
//                             backgroundColor: Colors.transparent,
//                             // getFontSize(index),
//                             // wordSpacing: .1,
//                           ),
//                         ));
//                       }
//
//                       return spans;
//                     }).toList(),
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         ),
//         // Padding(
//         //   padding: const EdgeInsets.only(bottom: AppSpaces.padding12),
//         //   child: Text(
//         //     index.toString(),
//         //     style: AppTextStyles.labelLarge,
//         //   ),
//         // ),
//       ],
//     );
//   }
// }
//
// void _showPopupMenu(BuildContext context, Offset position, int surah, int verse,
//     ValueNotifier<String> selectedSpan) {
//   final overlay = Overlay.of(context);
//
//   _currentPopup?.remove();
//   _currentPopup = null;
//   selectedSpan.value = "";
//
//   _currentPopup = OverlayEntry(
//     builder: (context) => Positioned(
//       left: position.dx - 50,
//       top: position.dy - 100,
//       child: HookBuilder(builder: (context) {
//         final isBookmarked = useState(false);
//         final isPlaying = useState(false);
//         final audioPlayer = useMemoized(() => AudioPlayer(), []);
//
//         Future<void> startAudio() async {
//           try {
//             isPlaying.value = true;
//             await audioPlayer.setUrl(
//               getAudioURLByVerse(surah, verse, "ar.minshawi"),
//             );
//             await audioPlayer.play();
//
//             audioPlayer.playerStateStream.listen((event) {
//               if (event.processingState == ProcessingState.completed) {
//                 isPlaying.value = false;
//               }
//             });
//           } catch (e) {
//             debugPrint('Error starting audio: $e');
//           }
//         }
//
//         Future<void> stopAudio() async {
//           try {
//             await audioPlayer.stop();
//             isPlaying.value = false;
//           } catch (e) {
//             debugPrint('Error stopping audio: $e');
//           }
//         }
//
//         return Material(
//           color: Colors.transparent,
//           child: Container(
//             padding: const EdgeInsets.all(8.0),
//             decoration: BoxDecoration(
//               color: Colors.white,
//               borderRadius: BorderRadius.circular(8.0),
//               boxShadow: const [
//                 BoxShadow(
//                   color: Colors.black26,
//                   blurRadius: 4.0,
//                 ),
//               ],
//             ),
//             child: Row(
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 IconButton(
//                   icon: Icon(
//                     isPlaying.value
//                         ? CupertinoIcons.stop
//                         : CupertinoIcons.play_arrow,
//                     color: isPlaying.value
//                         ? ColorManager.errorColor
//                         : Colors.black,
//                   ),
//                   onPressed: isPlaying.value ? stopAudio : startAudio,
//                 ),
//                 IconButton(
//                   icon: Icon(
//                     isBookmarked.value
//                         ? CupertinoIcons.bookmark_fill
//                         : CupertinoIcons.bookmark,
//                     color: isBookmarked.value
//                         ? ColorManager.secondaryColor
//                         : Colors.black,
//                   ),
//                   onPressed: () {
//                     isBookmarked.value = !isBookmarked.value;
//                   },
//                 ),
//                 IconButton(
//                   icon: const Icon(Icons.share),
//                   onPressed: () {
//                     // Add share logic here
//                   },
//                 ),
//               ],
//             ),
//           ),
//         );
//       }),
//     ),
//   );
//
//   overlay.insert(_currentPopup!);
// }
// class RichTextSection extends StatelessWidget {
//   final int index;
//   final dynamic jsonData;
//   final ValueNotifier<String> selectedSpan;
//
//   const RichTextSection({
//     Key? key,
//     required this.index,
//     required this.jsonData,
//     required this.selectedSpan,
//   }) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return Directionality(
//       textDirection: m.TextDirection.rtl,
//       child: Padding(
//         padding: const EdgeInsets.all(0.0),
//         child: SizedBox(
//           width: double.infinity,
//           child: RichText(
//             textDirection: m.TextDirection.rtl,
//             textAlign: (index == 1 || index == 2 || index > 570)
//                 ? TextAlign.center
//                 : TextAlign.center,
//             softWrap: true,
//             locale: const Locale("ar"),
//             text: TextSpan(
//               style: TextStyle(
//                 color: m.Colors.black,
//                 fontSize: 23.sp,
//               ),
//               children: getPageData(index).expand((e) {
//                 List<InlineSpan> spans = [];
//                 for (var i = e["start"]; i <= e["end"]; i++) {
//                   // Header
//                   if (i == 1) {
//                     spans.add(WidgetSpan(
//                       child: HeaderWidget(
//                         e: e,
//                         jsonData: jsonData,
//                       ),
//                     ));
//
//                     if (index != 187 && index != 1) {
//                       spans.add(const WidgetSpan(
//                         child: BasmallahTextWidget(index: 0),
//                       ));
//                     }
//
//                     if (index == 187) {
//                       spans.add(WidgetSpan(
//                         child: SizedBox(height: 10.h),
//                       ));
//                     }
//                   }
//
//                   // Verses
//                   spans.add(TextSpan(
//                     recognizer: LongPressGestureRecognizer()
//                       ..onLongPress = () {
//                         print(
//                           "Ayah ${e["surah"]}:$i\nAudio: ${getAudioURLByVerse(e["surah"], i, "ar.minshawi")}\nText: ${getVerseQCF(e["surah"], i)}",
//                         );
//                       }
//                       ..onLongPressDown = (_) {
//                         selectedSpan.value = " ${e["surah"]}$i";
//                       }
//                       ..onLongPressUp = () {
//                         selectedSpan.value = "";
//                         print("Finished long press");
//                       }
//                       ..onLongPressCancel = () {
//                         selectedSpan.value = "";
//                       },
//                     text: i == e["start"]
//                         ? "${getVerseQCF(e["surah"], i).replaceAll(" ", "").substring(0, 1)}\u200A${getVerseQCF(e["surah"], i).replaceAll(" ", "").substring(1)}"
//                         : getVerseQCF(e["surah"], i).replaceAll(' ', ''),
//                     style: TextStyle(
//                       color: Colors.black,
//                       height: (index == 1 || index == 2) ? 2.h : 1.95.h,
//                       fontFamily: "QCF_P${index.toString().padLeft(3, "0")}",
//                       fontSize: _getFontSize(index),
//                       backgroundColor: Colors.transparent,
//                     ),
//                   ));
//                 }
//                 return spans;
//               }).toList(),
//             ),
//           ),
//         ),
//       ),
//     );
//   }
//
//   double _getFontSize(int index) {
//     if (index == 1 || index == 2) {
//       return 27.sp;
//     } else if (index == 145 || index == 201) {
//       return (index == 532 || index == 533) ? 22.5.sp : 22.4.sp;
//     } else {
//       return 20.sp;
//     }
//   }
// }
