import 'package:flutter/material.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

class HizbDropDown extends StatefulWidget {
  const HizbDropDown({super.key});

  @override
  HizbDropDownState createState() => HizbDropDownState();
}

class HizbDropDownState extends State<HizbDropDown> {
  String? selectedHizb;

  final List<String> hizbList = [
    'الحزب الاول',
    'الحزب الثاني',
    'الحزب الثالث',
    'الحزب الرابع',
    'الحزب الخامس',
    'الحزب السادس',
    'الحزب السابع',
    'الحزب الثامن',
    'الحزب التاسع',
    'الحزب العاشر',
  ];

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<String>(
      value: selectedHizb,
      icon: const Icon(
        Icons.keyboard_arrow_down_rounded,
        color: ColorManager.primaryColor,
      ),
      decoration: InputDecoration(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppRadius.radius16),
          borderSide: BorderSide.none,
        ),
        filled: true,
        prefixIcon: const Icon(
          Icons.menu_book_rounded,
          color: ColorManager.primaryColor,
        ),
        fillColor: ColorManager.lightGrey.withOpacity(0.7),
      ),
      onChanged: (String? newValue) {
        setState(() {
          selectedHizb = newValue;
        });
      },
      items: hizbList.map<DropdownMenuItem<String>>((String value) {
        return DropdownMenuItem<String>(
          value: value,
          child: Text(
            value,
            style: const TextStyle(
              color: ColorManager.primaryColor,
            ),
          ),
        );
      }).toList(),
    );
  }
}
