import 'package:flutter/cupertino.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:quran_broadcast_app/src/core/shared/widgets/fields/text_field.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:quran_broadcast_app/src/features/quran/models/search_by_type.dart';

class SearchFieldWidget extends StatelessWidget {
  final ValueNotifier<String> searchValue;
  final TextEditingController searchController;
  final SearchByFieldType searchByFieldType;

  const SearchFieldWidget({
    super.key,
    required this.searchValue,
    required this.searchController,
    this.searchByFieldType = SearchByFieldType.surah,
  });

  @override
  Widget build(BuildContext context) {
    return BaseTextField(
      controller: searchController,
      hintText: searchByFieldType == SearchByFieldType.verse
          ? "ابحث عن آية"
          : searchByFieldType == SearchByFieldType.page
              ? "ابحث عن صفحة"
              : searchByFieldType == SearchByFieldType.juz
                  ? "ابحث عن جزء"
                  : searchByFieldType == SearchByFieldType.hizb
                      ? "ابحث عن حزب"
                      : "ابحث عن سورة",
      keyboardType: searchByFieldType == SearchByFieldType.page ||
              searchByFieldType == SearchByFieldType.juz ||
              searchByFieldType == SearchByFieldType.hizb
          ? TextInputType.number
          : TextInputType.text,
      maxLength: searchByFieldType == SearchByFieldType.page
          ? 3
          : searchByFieldType == SearchByFieldType.juz ||
                  searchByFieldType == SearchByFieldType.hizb
              ? 2
              : null,
      prefixIcon: const Icon(
        CupertinoIcons.search,
        color: ColorManager.primaryColor,
      ),
      onChanged: (v) {
        if (v.isEmpty) {
          searchValue.value = v;
          return;
        }

        if (searchByFieldType == SearchByFieldType.page) {
          final pageNumber = int.tryParse(v);
          if (pageNumber != null && pageNumber <= 604) {
            searchValue.value = v;
          } else {
            Fluttertoast.showToast(
              msg: "الصفحة غير موجودة",
              backgroundColor: ColorManager.errorColor,
            );
          }
        } else if (searchByFieldType == SearchByFieldType.juz) {
          final juzNumber = int.tryParse(v);
          if (juzNumber != null && juzNumber <= 30) {
            searchValue.value = v;
          } else {
            Fluttertoast.showToast(
              msg: "الجزء غير موجود",
              backgroundColor: ColorManager.errorColor,
            );
          }
        } else if (searchByFieldType == SearchByFieldType.hizb) {
          final hizbNumber = int.tryParse(v);
          if (hizbNumber != null && hizbNumber <= 60) {
            searchValue.value = v;
          } else {
            Fluttertoast.showToast(
              msg: "الحزب غير موجود",
              backgroundColor: ColorManager.errorColor,
            );
          }
        } else {
          searchValue.value = v;
        }
      },
    );
  }
}
