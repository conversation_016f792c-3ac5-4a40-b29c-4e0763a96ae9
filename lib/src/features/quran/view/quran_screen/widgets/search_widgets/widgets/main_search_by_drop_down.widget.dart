import 'package:flutter/material.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:quran_broadcast_app/src/features/quran/models/search_by_type.dart';
import 'package:xr_helper/xr_helper.dart';

class MainSearchByDropDown extends StatelessWidget {
  final ValueNotifier<SearchByFieldType> selectedSearchBy;
  final TextEditingController searchController;
  final ValueNotifier<String> searchValue;
  final ValueNotifier<List<num>> pageNumbers;
  final ValueNotifier<Map?> ayaFiltered;

  const MainSearchByDropDown({
    super.key,
    required this.selectedSearchBy,
    required this.searchController,
    required this.searchValue,
    required this.pageNumbers,
    required this.ayaFiltered,
  });

  @override
  Widget build(BuildContext context) {
    return FittedBox(
      fit: BoxFit.scaleDown,
      child: Padding(
        padding: EdgeInsets.only(
            bottom: selectedSearchBy.value == SearchByFieldType.page ||
                    selectedSearchBy.value == SearchByFieldType.juz ||
                    selectedSearchBy.value == SearchByFieldType.hizb
                ? AppSpaces.padding20
                : 0),
        child: Align(
          alignment: Alignment.centerLeft,
          child: DropdownButton<SearchByFieldType>(
            borderRadius: BorderRadius.circular(AppRadius.radius12),
            value: selectedSearchBy.value,
            icon: const Icon(
              Icons.keyboard_arrow_down_rounded,
              color: ColorManager.primaryColor,
            ),
            underline: const SizedBox.shrink(),
            onChanged: (SearchByFieldType? value) {
              if (value != null) {
                selectedSearchBy.value = value;
                searchController.clear();
                searchValue.value = '';
                pageNumbers.value = [];
                ayaFiltered.value = null;
              }
            },
            items: SearchByFieldType.values
                .where(
              (element) => element != SearchByFieldType.hizb,
            )
                .map(
              (e) {
                return DropdownMenuItem(
                  value: e,
                  child: Text(
                    e.value,
                    style: AppTextStyles.labelLarge,
                  ),
                );
              },
            ).toList(),
          ),
        ),
      ),
    );
  }
}
