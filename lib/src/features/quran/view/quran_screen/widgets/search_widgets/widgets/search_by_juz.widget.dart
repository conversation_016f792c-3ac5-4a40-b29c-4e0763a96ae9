import 'package:flutter/material.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

class JuzDropDown extends StatefulWidget {
  const JuzDropDown({super.key});

  @override
  JuzDropDownState createState() => JuzDropDownState();
}

class JuzDropDownState extends State<JuzDropDown> {
  String? selectedJuz;

  final List<String> juzList = [
    'الجزء الاول',
    'الجزء الثاني',
    'الجزء الثالث',
    'الجزء الرابع',
    'الجزء الخامس',
    'الجزء السادس',
    'الجزء السابع',
    'الجزء الثامن',
    'الجزء التاسع',
    'الجزء العاشر',
  ];

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<String>(
      value: selectedJuz,
      icon: const Icon(
        Icons.keyboard_arrow_down_rounded,
        color: ColorManager.primaryColor,
      ),
      decoration: InputDecoration(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppRadius.radius16),
          borderSide: BorderSide.none,
        ),
        filled: true,
        prefixIcon: const Icon(
          Icons.menu_book_rounded,
          color: ColorManager.primaryColor,
        ),
        fillColor: ColorManager.lightGrey.withOpacity(0.7),
      ),
      onChanged: (String? newValue) {
        setState(() {
          selectedJuz = newValue;
        });
      },
      items: juzList.map<DropdownMenuItem<String>>((String value) {
        return DropdownMenuItem<String>(
          value: value,
          child: Text(
            value,
            style: const TextStyle(
              color: ColorManager.primaryColor,
            ),
          ),
        );
      }).toList(),
    );
  }
}
