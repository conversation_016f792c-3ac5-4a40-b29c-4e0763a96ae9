import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:quran/quran.dart';
import 'package:quran_broadcast_app/src/features/main_screen/view/main.screen.dart';
import 'package:quran_broadcast_app/src/features/quran/models/search_by_type.dart';
import 'package:quran_broadcast_app/src/features/quran/models/surah_model.dart';
import 'package:quran_broadcast_app/src/features/quran/view/quran_screen/widgets/search_widgets/widgets/main_search_by_drop_down.widget.dart';
import 'package:quran_broadcast_app/src/features/quran/view/quran_screen/widgets/search_widgets/widgets/search_field.widget.dart';
import 'package:string_validator/string_validator.dart';
import 'package:xr_helper/xr_helper.dart';

class QuranSearchSection extends HookWidget {
  final ValueNotifier<String> searchValue;
  final ValueNotifier<List<num>> pageNumbers;
  final ValueNotifier<Map?> ayaFiltered;
  final ValueNotifier<SearchByFieldType> selectedSearchBy;

  const QuranSearchSection({
    super.key,
    required this.searchValue,
    required this.selectedSearchBy,
    required this.pageNumbers,
    required this.ayaFiltered,
  });

  @override
  Widget build(BuildContext context) {
    final searchController = useTextEditingController();

    useEffect(() {
      void handleSearch() {
        if (searchValue.value.isEmpty) {
          filteredData.value =
              List.from(suraJsonData.value.map((e) => SurahModel.fromMap(e)));

          pageNumbers.value = [];
          ayaFiltered.value = null;
        } else {
          if (selectedSearchBy.value == SearchByFieldType.page &&
              isNumeric(searchValue.value)) {
            final pageNumber = toInt(searchValue.value);
            if (pageNumber >= 1 && pageNumber <= 604) {
              pageNumbers.value = [pageNumber];
            }
          }
          if (selectedSearchBy.value == SearchByFieldType.verse) {
            ayaFiltered.value = searchWords(searchValue.value);
          }

          if (selectedSearchBy.value == SearchByFieldType.surah) {
            filteredData.value = List.from(suraJsonData.value
                .where((sura) {
                  final arName = sura['name'].toLowerCase();
                  final suraName = sura['englishName'].toLowerCase();
                  final suraNameTranslated = getSurahNameArabic(sura["number"]);
                  return suraName.contains(searchValue.value.toLowerCase()) ||
                      suraNameTranslated
                          .contains(searchValue.value.toLowerCase()) ||
                      arName.contains(searchValue.value.toLowerCase());
                })
                .map<SurahModel>((sura) => SurahModel.fromMap(sura))
                .toList());
          }

          if (selectedSearchBy.value == SearchByFieldType.juz) {
            final juz = toInt(searchValue.value);
            if (juz >= 1 && juz <= 30) {
              final surahAndVerses = getSurahAndVersesFromJuz(juz.toInt());
              filteredData.value = List.from(suraJsonData.value
                  .where((sura) => surahAndVerses.containsKey(sura['number']))
                  .map<SurahModel>((sura) => SurahModel.fromMap(sura))
                  .toList());
            }
          }

          // if (selectedSearchBy.value == SearchByFieldType.hizb) {
          //   final hizb = toInt(searchValue.value);
          //   if (hizb >= 1 && hizb <= 60) {
          //     filteredData.value = List.from(suraJsonData
          //         .where((sura) => sura['hizb'] == hizb)
          //         .map<SurahModel>((sura) => SurahModel.fromMap(sura))
          //         .toList());
          //   }
          // }
        }
      }

      WidgetsBinding.instance.addPostFrameCallback((_) {
        handleSearch();
      });

      return () {};
    }, [searchValue.value]);

    return Padding(
      padding: const EdgeInsets.only(
        left: AppSpaces.padding12,
      ),
      child: Row(
        children: [
          // * Search Fields
          Expanded(
            flex: 3,
            child: GetSelectedSearchWidget(
              searchValue: searchValue,
              searchController: searchController,
              selectedSearchBy: selectedSearchBy.value,
            ),
          ),

          AppGaps.gap8,

          // * Main Search By (Surah, Verse, Page, Juz, Hizb)
          Flexible(
            child: MainSearchByDropDown(
              selectedSearchBy: selectedSearchBy,
              ayaFiltered: ayaFiltered,
              pageNumbers: pageNumbers,
              searchValue: searchValue,
              searchController: searchController,
            ),
          ),
        ],
      ),
    );
  }
}

class GetSelectedSearchWidget extends StatelessWidget {
  final ValueNotifier<String> searchValue;
  final TextEditingController searchController;
  final SearchByFieldType selectedSearchBy;

  const GetSelectedSearchWidget(
      {super.key,
      required this.searchValue,
      required this.searchController,
      required this.selectedSearchBy});

  @override
  Widget build(BuildContext context) {
    switch (selectedSearchBy) {
      case SearchByFieldType.surah:
        return SearchFieldWidget(
          searchController: searchController,
          searchValue: searchValue,
          searchByFieldType: SearchByFieldType.surah,
        );
      case SearchByFieldType.verse:
        return SearchFieldWidget(
          searchController: searchController,
          searchValue: searchValue,
          searchByFieldType: SearchByFieldType.verse,
        );
      case SearchByFieldType.page:
        return SearchFieldWidget(
          searchController: searchController,
          searchValue: searchValue,
          searchByFieldType: SearchByFieldType.page,
        );
      case SearchByFieldType.juz:
        return SearchFieldWidget(
          searchController: searchController,
          searchValue: searchValue,
          searchByFieldType: SearchByFieldType.juz,
        );
      case SearchByFieldType.hizb:
        return SearchFieldWidget(
          searchController: searchController,
          searchValue: searchValue,
          searchByFieldType: SearchByFieldType.hizb,
        );
    }
  }
}
