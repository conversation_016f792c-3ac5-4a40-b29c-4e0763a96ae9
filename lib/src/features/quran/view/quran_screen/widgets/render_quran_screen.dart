import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:quran_broadcast_app/src/features/quran/view/quran_screen/quran.screen.dart';
import 'package:quran_broadcast_app/src/features/quran/view/widgets/navigate_to_mark_floating_button.widget.dart';

class RenderQuranScreen extends HookWidget {
  const RenderQuranScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    useAutomaticKeepAlive();

    useMemoized(() {
      precacheImage(
          const AssetImage('assets/images/quran_background.png'), context);
    });

    return const Stack(
      alignment: Alignment.bottomCenter,
      children: [
        QuranScreen(),
        NavigateToBookmarkFloatingButtonWidget(),
      ],
    );
  }
}
