import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:quran/quran.dart';
import 'package:quran_broadcast_app/src/core/shared/widgets/lists/base_list.dart';
import 'package:quran_broadcast_app/src/features/main_screen/view/main.screen.dart';
import 'package:quran_broadcast_app/src/features/quran/models/search_by_type.dart';
import 'package:quran_broadcast_app/src/features/quran/models/surah_model.dart';
import 'package:quran_broadcast_app/src/features/quran/view/quran_screen/widgets/surah_widgets/surah_item.widget.dart';

class PageNumberFilteredListWidget extends StatelessWidget {
  final List<num> pageNumbers;

  const PageNumberFilteredListWidget({
    super.key,
    required this.pageNumbers,
  });

  @override
  Widget build(BuildContext context) {
    return BaseList(
      data: pageNumbers,
      itemBuilder: (data, index) {
        final pageData = getPageData(pageNumbers[index].toInt());

        final surahModel = filteredData.value.firstWhereOrNull(
            (element) => element.suraNumber == pageData[0]["surah"]);

        return SurahItemWidget(
          surah: surahModel ?? SurahModel(),
          pageNumber: pageNumbers[index],
          selectedSearchBy: SearchByFieldType.page,
        );
      },
    );
  }
}
