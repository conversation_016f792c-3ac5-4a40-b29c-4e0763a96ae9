import 'package:flutter/material.dart';
import 'package:quran/quran.dart';
import 'package:quran_broadcast_app/src/core/shared/widgets/lists/base_list.dart';
import 'package:quran_broadcast_app/src/features/main_screen/view/main.screen.dart';
import 'package:quran_broadcast_app/src/features/quran/models/search_by_type.dart';
import 'package:string_validator/string_validator.dart';
import 'package:xr_helper/xr_helper.dart';

import 'surah_item.widget.dart';

class SurahList extends StatelessWidget {
  final String searchValue;
  final SearchByFieldType selectedSearchBy;

  const SurahList({
    super.key,
    required this.searchValue,
    this.selectedSearchBy = SearchByFieldType.surah,
  });

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: filteredData,
      builder: (context, value, child) {
        return BaseList(
          data: value,
          padding: const EdgeInsets.only(
            top: AppSpaces.padding12,
            left: AppSpaces.padding8,
            right: AppSpaces.padding8,
          ),
          itemBuilder: (surah, index) {
            final copiedSurah = surah.copyWith(
              ayahCount: getVerseCount(surah.suraNumber!),
            );

            return SurahItemWidget(
              surah: copiedSurah,
              selectedSearchBy: selectedSearchBy,
              pageNumber: toInt(searchValue).isNaN ||
                      selectedSearchBy == SearchByFieldType.juz
                  ? getPageNumber(copiedSurah.suraNumber ?? 0, 1)
                  : toInt(searchValue),
            );
          },
        );
      },
    );
  }
}
