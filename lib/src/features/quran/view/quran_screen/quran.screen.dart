import 'package:flutter/material.dart'; // import 'package:quran/dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:quran_broadcast_app/src/core/shared/widgets/background_widgets/background_body.dart';
import 'package:quran_broadcast_app/src/core/shared/widgets/loading/loading_widget.dart';
import 'package:quran_broadcast_app/src/features/quran/models/search_by_type.dart';
import 'package:quran_broadcast_app/src/features/quran/view/quran_screen/widgets/filtered_list_widgets/aya_filtered_list.widget.dart';
import 'package:quran_broadcast_app/src/features/quran/view/quran_screen/widgets/filtered_list_widgets/page_number_filtered_list.widget.dart';
import 'package:quran_broadcast_app/src/features/quran/view/quran_screen/widgets/search_widgets/search_section.widget.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/surah_widgets/surah_list.widget.dart';

class QuranScreen extends HookWidget {
  final bool isLoading;

  const QuranScreen({
    super.key,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final searchValue = useState<String>('');
    final pageNumbers = useState<List<num>>([]);
    final ayaFiltered = useState<Map?>(null);
    final selectedSearchBy = useState(SearchByFieldType.surah);

    return BackgroundBody(
      child: Padding(
        padding: const EdgeInsets.all(AppSpaces.screenPadding),
        child: isLoading
            ? const LoadingWidget()
            : Column(
                children: [
                  // * Search Section
                  QuranSearchSection(
                    searchValue: searchValue,
                    pageNumbers: pageNumbers,
                    ayaFiltered: ayaFiltered,
                    selectedSearchBy: selectedSearchBy,
                  ),

                  AppGaps.gap8,

                  // * List Of Surah (Searched Page - Aya - Surah)
                  Expanded(
                    child: GetSelectedSurahListView(
                      searchValue: searchValue,
                      selectedSearchBy: selectedSearchBy,
                      pageNumbers: pageNumbers,
                      ayaFiltered: ayaFiltered,
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}

class GetSelectedSurahListView extends StatelessWidget {
  final ValueNotifier<SearchByFieldType> selectedSearchBy;
  final ValueNotifier<List<num>> pageNumbers;
  final ValueNotifier<Map?> ayaFiltered;
  final ValueNotifier<String> searchValue;

  const GetSelectedSurahListView({
    super.key,
    required this.selectedSearchBy,
    required this.pageNumbers,
    required this.searchValue,
    required this.ayaFiltered,
  });

  @override
  Widget build(BuildContext context) {
    // * Page Number List
    if (pageNumbers.value.isNotEmpty) {
      return PageNumberFilteredListWidget(
        pageNumbers: pageNumbers.value,
      );
    }

    // * Aya Filtered List
    if (ayaFiltered.value != null) {
      return AyaFilteredListWidget(
        ayatFiltered: ayaFiltered.value!,
      );
    }

    // * List Of Surah
    return SurahList(
      searchValue: searchValue.value,
      selectedSearchBy: selectedSearchBy.value,
    );
  }
}
