import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quran_broadcast_app/src/features/settings/providers/settings.providers.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xr_helper/xr_helper.dart';

class SocialWidget extends ConsumerWidget {
  const SocialWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsController = ref.watch(settingsControllerNotifierProvider);

    final socials = settingsController.settings.value.socials;

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(
        horizontal: AppSpaces.padding12,
      ),
      padding: const EdgeInsets.symmetric(
        vertical: AppSpaces.padding12,
        horizontal: AppSpaces.padding16,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppRadius.radius16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5),
            spreadRadius: 1,
            blurRadius: 7,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(CupertinoIcons.at),
              AppGaps.gap8,
              Text(
                'مواقع التواصل الاجتماعي',
                style: AppTextStyles.subTitle,
              ),
            ],
          ),
          AppGaps.gap16,
          Wrap(
              alignment: WrapAlignment.center,
              runAlignment: WrapAlignment.center,
              runSpacing: 16,
              spacing: 16,
              children: socials.map(
                (e) {
                  return InkWell(
                    onTap: () async {
                      launchUrl(Uri.parse(e.url));
                    },
                    child: BaseCachedImage(
                      e.icon,
                      height: 40,
                      width: 40,
                      fit: BoxFit.cover,
                    ),
                  );
                },
              ).toList()

              // [
              //   const Icon(
              //     FontAwesomeIcons.facebook,
              //     color: Color(0xFF1877F2), // Facebook Blue
              //     size: 40,
              //   ),
              //   const Icon(
              //     FontAwesomeIcons.twitter,
              //     color: Color(0xFF1DA1F2), // Twitter Blue
              //     size: 40,
              //   ),
              //   const Icon(
              //     FontAwesomeIcons.instagram,
              //     color: Color(0xFFDC2743), // Instagram Pink
              //     size: 40,
              //   ),
              //   const Icon(
              //     FontAwesomeIcons.youtube,
              //     color: Color(0xFFFF0000), // YouTube Red
              //     size: 40,
              //   ),
              //   const Icon(
              //     FontAwesomeIcons.telegram,
              //     color: Color(0xFF0088CC), // Telegram Blue
              //     size: 40,
              //   ),
              //   const Icon(
              //     FontAwesomeIcons.whatsapp,
              //     color: Color(0xFF25D366), // WhatsApp Green
              //     size: 40,
              //   ),
              //   Assets.images.audioStreamLogo.image(
              //     width: 50,
              //     fit: BoxFit.cover,
              //   ),
              //   const Icon(
              //     FontAwesomeIcons.soundcloud,
              //     color: Color(0xFFFF5500), // SoundCloud Orange
              //     size: 40,
              //   ),
              // ],
              ),
        ],
      ),
    );
  }
}
