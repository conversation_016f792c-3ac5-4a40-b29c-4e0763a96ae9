import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:quran_broadcast_app/src/features/settings/providers/settings.providers.dart';
import 'package:share_plus/share_plus.dart';
import 'package:xr_helper/xr_helper.dart';

class ShareAppWidget extends ConsumerWidget {
  const ShareAppWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsController = ref.watch(settingsControllerNotifierProvider);

    final android = settingsController.settings.value.android;
    final ios = settingsController.settings.value.apple;

    if (android.url.isEmpty && ios.url.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(
        horizontal: AppSpaces.padding12,
      ),
      padding: const EdgeInsets.symmetric(
        vertical: AppSpaces.padding12,
        horizontal: AppSpaces.padding16,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppRadius.radius16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5),
            spreadRadius: 1,
            blurRadius: 7,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              const Icon(CupertinoIcons.share),
              AppGaps.gap8,
              Text(
                'مشاركة التطبيق',
                style: AppTextStyles.subTitle,
              ),
            ],
          ),
          Row(
            children: [
              if (android.url.isNotEmpty) ...[
                InkWell(
                  onTap: () {
                    final shareText =
                        "حمل تطبيق القرآن الكريم من جوجل بلاي الآن !\n${android.url}";
                    Share.share(shareText);
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: ColorManager.blueGrey,
                        width: 1,
                      ),
                    ),
                    child: BaseCachedImage(
                      android.icon,
                      height: 27,
                      width: 27,
                      fit: BoxFit.cover,
                    ),
                    // Assets.icons.android.image(
                    //   height: 27,
                    //   fit: BoxFit.cover,
                    // ),
                  ),
                ),
                AppGaps.gap12,
              ],
              if (ios.url.isNotEmpty)
                InkWell(
                  onTap: () {
                    final shareText =
                        "حمل تطبيق القرآن الكريم من آبل ستور الآن !\n${ios.url}";
                    Share.share(shareText);
                  },
                  child: Container(
                    // height: 30,
                    // width: 80,
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: ColorManager.blueGrey,
                        width: 1,
                      ),
                    ),
                    child: BaseCachedImage(
                      ios.icon,
                      height: 29,
                      width: 29,
                      fit: BoxFit.cover,
                    ),
                    // /Assets.icons.apple.image(
                    //   height: 30,
                    //   fit: BoxFit.cover,
                    // ),
                  ),
                ),
            ],
          )
        ],
      ),
    );
  }
}
