import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:quran_broadcast_app/src/features/notifications/controller/notification_controller.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/shared/services/home_widgets_service/home_widget.service.dart';

class SummerTimerSwitchWidget extends HookConsumerWidget {
  const SummerTimerSwitchWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final summerTimer =
        useState(GetStorageService.getData(key: LocalKeys.summerTime) ?? false);
    final notificationController =
        ref.watch(notificationControllerProvider(ref));

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(
        horizontal: AppSpaces.padding12,
      ),
      padding: const EdgeInsets.symmetric(
        vertical: AppSpaces.padding4,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppRadius.radius16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5),
            spreadRadius: 1,
            blurRadius: 7,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: SwitchListTile(
        value: summerTimer.value,
        onChanged: (value) async {
          summerTimer.value = value;

          await GetStorageService.setData(
            key: LocalKeys.summerTime,
            value: value,
          );

          await notificationController.cancelAllScheduledNotifications();

          notificationController.schedulePrayerNotifications();

          // Update current widget and reschedule all future updates when summer time changes
          HomeWidgetService.update(context, ref: ref);
          await HomeWidgetService.scheduleAllFutureWidgetUpdates();
        },
        title: Text(
          'توقيت صيفي',
          style: AppTextStyles.subTitle,
        ),
      ),
    );
  }
}
