import 'package:flutter/material.dart';
import 'package:quran_broadcast_app/src/features/settings/view/widgets/share_app_widget.dart';
import 'package:quran_broadcast_app/src/features/settings/view/widgets/social_widget.dart';
import 'package:quran_broadcast_app/src/features/settings/view/widgets/summer_timer_switch.widget.dart';
import 'package:xr_helper/xr_helper.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Column(
      children: [
        AppGaps.gap48,
        SummerTimerSwitchWidget(),
        AppGaps.gap24,
        ShareAppWidget(),
        AppGaps.gap24,
        SocialWidget(),
        Spacer(),
      ],
    );
  }
}
