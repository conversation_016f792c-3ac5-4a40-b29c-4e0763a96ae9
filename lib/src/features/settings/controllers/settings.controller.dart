import 'package:flutter/cupertino.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:quran_broadcast_app/src/features/settings/models/settings_model.dart';
import 'package:quran_broadcast_app/src/features/settings/repositories/settings.repository.dart';
import 'package:xr_helper/xr_helper.dart';

class SettingsController extends BaseVM {
  final SettingsRepository settingsRepo;

  SettingsController({
    required this.settingsRepo,
  });

  ValueNotifier<SettingsModel> settings = ValueNotifier(SettingsModel.empty());

  // get current update date lastUpdatedAt
  String? get formattedLastUpdatedAt =>
      settings.value.lastUpdatedAt.formatDateToStringWithTime;

  Future<SettingsModel> getSettings() async {
    return await baseFunction(
      () async {
        final haveInternet = await InternetConnection().hasInternetAccess;

        if (haveInternet) {
          settings.value = await settingsRepo.getSettings();

          saveSettings(settings: settings.value);

          GetStorageService.setData(
            key: LocalKeys.lastUpdateLocalTime,
            value: settings.value.lastUpdatedAt?.toIso8601String(),
          );
        } else {
          settings.value = await getSettingsFromLocal();
        }

        return settings.value;
      },
    );
  }

  Future<void> saveSettings({
    required SettingsModel settings,
  }) async {
    return await baseFunction(
      () async {
        GetStorageService.setData(
          key: LocalKeys.settings,
          value: settings.toJson(),
        );
      },
    );
  }

  Future<SettingsModel> getSettingsFromLocal() async {
    return await baseFunction(
      () async {
        final settings =
            await GetStorageService.getData(key: LocalKeys.settings);

        if (settings != null) {
          final settingsModel = SettingsModel.fromJson(settings);

          return settingsModel;
        }

        return SettingsModel.empty();
      },
    );
  }
}
