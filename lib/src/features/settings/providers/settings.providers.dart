import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:quran_broadcast_app/src/core/shared/providers/network_api_service_provider.dart';
import 'package:quran_broadcast_app/src/features/settings/controllers/settings.controller.dart';
import 'package:quran_broadcast_app/src/features/settings/repositories/settings.repository.dart';

// * Settings Repo Provider ========================================
final settingsRepoProvider = Provider<SettingsRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return SettingsRepository(networkApiService: networkApiService);
});

// * Settings Change Notifier Provider ========================================
final settingsControllerNotifierProvider =
    ChangeNotifierProvider<SettingsController>(
  (ref) {
    final settingsRepo = ref.watch(settingsRepoProvider);

    return SettingsController(
      settingsRepo: settingsRepo,
    );
  },
);

// * Settings Provider ========================================
final settingsControllerProvider = Provider<SettingsController>(
  (ref) {
    final settingsRepo = ref.watch(settingsRepoProvider);

    return SettingsController(
      settingsRepo: settingsRepo,
    );
  },
);
