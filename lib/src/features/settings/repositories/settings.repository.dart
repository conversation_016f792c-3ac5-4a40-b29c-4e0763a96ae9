import 'package:quran_broadcast_app/src/core/consts/network/api_endpoints.dart';
import 'package:quran_broadcast_app/src/features/settings/models/settings_model.dart';
import 'package:xr_helper/xr_helper.dart';

class SettingsRepository with BaseRepository {
  final BaseApiServices networkApiService;

  SettingsRepository({
    required this.networkApiService,
  });

  Future<SettingsModel> getSettings() async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.home;

        final response = await networkApiService.getResponse(url);
        return SettingsModel.fromJson(response['data']);
      },
    );
  }
}
