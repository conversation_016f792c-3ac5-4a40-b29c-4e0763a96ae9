import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:quran_broadcast_app/src/core/shared/widgets/loading/loading_widget.dart';
import 'package:quran_broadcast_app/src/core/shared/widgets/next_previous_widget/next_previous.widget.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:quran_broadcast_app/src/features/calendar/controllers/calendar.controller.dart';
import 'package:quran_broadcast_app/src/features/calendar/models/calendar_model.dart';
import 'package:quran_broadcast_app/src/features/calendar/providers/calendar.providers.dart';
import 'package:selectable/selectable.dart';
import 'package:xr_helper/xr_helper.dart';

class TodayWisdomScreen extends HookConsumerWidget {
  const TodayWisdomScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final calendarController = ref.watch(calendarControllerNotifierProvider);
    final calendarData = CalendarController.calendar.value;

    final isFirstDayInCalendarDays = CalendarController
            .calendar.value.firstOrNull?.days.firstOrNull?.gregorianDate ==
        calendarController.currentDateNotifier.value.formatDateToString;

    final isLastDayInCalendarDays = CalendarController
            .calendar.value.lastOrNull?.days.lastOrNull?.gregorianDate ==
        calendarController.currentDateNotifier.value.formatDateToString;

    final currentDayData = calendarController.calendarByDateWisdomScreen(
        calendarController.currentDateNotifier.value);

    final currentMonthData = calendarData.firstWhereOrNull(
            (element) => element.days.contains(currentDayData)) ??
        CalendarModel.empty();

    final currentHijriMonth = currentMonthData.hijriMonths.firstWhereOrNull(
            (element) =>
                element.hijriMonthNumber.toString() ==
                currentDayData.hijriMonthNumber) ??
        HijriMonth.empty();

    final isToday = calendarController.currentDateNotifier.value.isToday();

    return ListView(
      padding: const EdgeInsets.only(
        bottom: AppSpaces.padding8,
        right: AppSpaces.screenPadding,
        left: AppSpaces.screenPadding,
      ),
      children: [
        AppGaps.gap24,
        NextPreviousWidget(
          onNext: isLastDayInCalendarDays || isToday
              ? null
              : () => calendarController.changeDate(1),
          onPrevious: isFirstDayInCalendarDays
              ? null
              : () => calendarController.changeDate(-1),
          centerWidget: Expanded(
            child: Text.rich(
              textAlign: TextAlign.center,
              TextSpan(
                children: [
                  TextSpan(
                    text: '${currentDayData.hijriDayNumber} ',
                    style: AppTextStyles.subTitle.copyWith(
                      fontSize: 22,
                      color: ColorManager.primaryColor,
                    ),
                  ),
                  TextSpan(
                    text: '${currentHijriMonth.hijriMonthName} ',
                    style: AppTextStyles.subTitle.copyWith(fontSize: 16),
                  ),
                  TextSpan(
                    text: '(${currentHijriMonth.hijriMonthNumber}) ',
                    style: AppTextStyles.subTitle.copyWith(fontSize: 12),
                  ),
                  TextSpan(
                    text: '${currentHijriMonth.hijriYear}هـ \n',
                    style: AppTextStyles.subTitle,
                  ),
                  TextSpan(
                    text: '${currentDayData.gregorianDayNumber} ',
                    style: AppTextStyles.subTitle.copyWith(
                      fontSize: 22,
                      color: ColorManager.primaryColor,
                    ),
                  ),
                  TextSpan(
                    text: '${currentMonthData.gregorianMonthName} ',
                    style: AppTextStyles.subTitle.copyWith(fontSize: 16),
                  ),
                  TextSpan(
                    text: '(${currentMonthData.gregorianMonthNumber}) ',
                    style: AppTextStyles.subTitle.copyWith(fontSize: 12),
                  ),
                  TextSpan(
                    text: '${currentMonthData.gregorianYear}م',
                    style: AppTextStyles.subTitle,
                  ),
                ],
              ),
            ),
          ),
        ),
        AppGaps.gap48,
        if (currentDayData.wisdom.isEmpty)
          Center(
              child: Text(
            'لا يوجد حكمة لهذا اليوم',
            style: AppTextStyles.subTitle,
          ))
        else
          SingleChildScrollView(
            padding: const EdgeInsets.all(AppSpaces.screenPadding),
            child: Selectable(
              child: HtmlWidget(
                currentDayData.wisdom,
                customStylesBuilder: (element) {
                  if (element.classes.contains('ql-align-center')) {
                    return {'text-align': 'center'};
                  }
                  return null;
                },
                onLoadingBuilder: (context, element, loadingProgress) =>
                    const Center(child: LoadingWidget()),
              ),
            ),
          ),
      ],
    );
  }
}
