import 'dart:math' show pi;

import 'package:flutter/cupertino.dart';
import 'package:flutter_qiblah/flutter_qiblah.dart';
import 'package:geolocator/geolocator.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:quran_broadcast_app/src/features/qiblah/view/widgets/qibla_place_holder.widget.dart';
import 'package:xr_helper/xr_helper.dart';

class QiblahCompassWidget extends StatelessWidget {
  const QiblahCompassWidget({super.key});

  @override
  Widget build(BuildContext context) {
    const _compassSvg = CompassWidget();

    const _needleSvg = NeedleWidget();

    return FutureBuilder<Position>(
      future: _getCurrentLocation(),
      builder: (_, AsyncSnapshot<Position> locationSnapshot) {
        if (locationSnapshot.connectionState == ConnectionState.waiting) {
          return const QiblaPlaceHolderWidget();
        }

        if (!locationSnapshot.hasData) {
          return Center(
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "من فضلك قم بتفعيل خدمات الموقع !",
                    style: AppTextStyles.labelLarge.copyWith(
                      color: ColorManager.secondaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  AppGaps.gap16,
                  const QiblaPlaceHolderWidget()
                ],
              ),
            ),
          );
        }

        final currentPosition = locationSnapshot.data!;

        return StreamBuilder<QiblahDirection>(
          stream: FlutterQiblah.qiblahStream,
          builder: (_, AsyncSnapshot<QiblahDirection> snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const QiblaPlaceHolderWidget();
            }

            if (!snapshot.hasData) {
              return const Text("Error: No data available");
            }

            final qiblahDirection = snapshot.data!;

            final distance = _calculateDistance(
              currentPosition.latitude,
              currentPosition.longitude,
              21.4225, // Latitude of Mecca
              39.8262, // Longitude of Mecca
            );

            return Center(
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          CupertinoIcons.location_circle,
                          color: ColorManager.secondaryColor,
                        ),
                        AppGaps.gap4,
                        Text(
                          "${distance.toStringAsFixed(2)} كم",
                          style: AppTextStyles.labelLarge,
                        ),
                      ],
                    ).paddingOnly(bottom: AppSpaces.padding12),
                    Stack(
                      alignment: Alignment.center,
                      children: <Widget>[
                        Transform.rotate(
                          angle: (qiblahDirection.direction * (pi / 180) * -1),
                          child: _compassSvg,
                        ),
                        Transform.rotate(
                          angle: (qiblahDirection.qiblah * (pi / 180) * -1),
                          alignment: Alignment.center,
                          child: _needleSvg,
                        ),
                      ],
                    ),
                    Text("${qiblahDirection.offset.toStringAsFixed(3)}°")
                        .paddingOnly(top: AppSpaces.padding12),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Future<Position> _getCurrentLocation() async {
    bool serviceEnabled;
    LocationPermission permission;
    // if (Platform.isIOS) {
    //   // use location package
    //   final permission = await loc.Location().requestPermission();
    //
    //   Log.e('asfsafafsa ${permission.toString()}');
    //
    //   final locationData = await loc.Location().getLocation();
    //
    //   Log.e('ffffffffff ${locationData.toString()}');
    //
    //   if (permission == loc.PermissionStatus.denied) {
    //     return Future.error('Location permissions are denied');
    //   } else if (permission == loc.PermissionStatus.deniedForever) {
    //     return Future.error(
    //         'Location permissions are permanently denied, we cannot request permissions.');
    //   }
    //
    //   final serviceEnabled = await loc.Location().serviceEnabled();
    //
    //   if (!serviceEnabled) {
    //     return Future.error('Location services are disabled.');
    //   }
    //
    //
    //   return Position(
    //     latitude: locationData.latitude!,
    //     longitude: locationData.longitude!,
    //     timestamp: DateTime.now(),
    //     accuracy: 0,
    //     altitude: 0,
    //     heading: 0,
    //     speed: 0,
    //     speedAccuracy: 0,
    //     altitudeAccuracy: 0,
    //     headingAccuracy: 0,
    //     floor: 0,
    //     isMocked: false,
    //   );
    // } else {
    serviceEnabled = await Geolocator.isLocationServiceEnabled();

    if (!serviceEnabled) {
      return Future.error('Location services are disabled.');
    }

    permission = await Geolocator.checkPermission();

    if (permission != LocationPermission.always &&
        permission != LocationPermission.whileInUse) {
      permission = await Geolocator.requestPermission();
    }

    if (permission == LocationPermission.deniedForever) {
      return Future.error(
          'Location permissions are permanently denied, we cannot request permissions.');
    }

    return await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
      accuracy: LocationAccuracy.low,
    ));
    // }
  }

  double _calculateDistance(double startLatitude, double startLongitude,
      double endLatitude, double endLongitude) {
    final distance = Geolocator.distanceBetween(
            startLatitude, startLongitude, endLatitude, endLongitude) /
        1000; // Convert to kilometers

    return distance;
  }
}
