import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_qiblah/flutter_qiblah.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:location/location.dart' as loc;
import 'package:permission_handler/permission_handler.dart';
import 'package:quran_broadcast_app/src/core/shared/widgets/navigations/controller/bottom_nav_bar.controller.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:quran_broadcast_app/src/features/qiblah/view/widgets/qibla_place_holder.widget.dart';
import 'package:quran_broadcast_app/src/features/qiblah/view/widgets/qiblah_compass.widget.dart';
import 'package:xr_helper/xr_helper.dart';

class QiblahCompass extends HookConsumerWidget {
  const QiblahCompass({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(bottomNavigationControllerProvider);
    final locationStatus = useState<LocationStatus?>(null);

    useEffect(() {
      Future<void> _checkLocationStatus() async {
        await Permission.location.request();

        if (Platform.isIOS) {
          // use location package
          final permission = await loc.Location().requestPermission();

          if (permission != loc.PermissionStatus.granted) {
            return;
          }

          final location = loc.Location();

          final serviceEnabled = await location.serviceEnabled();

          if (!serviceEnabled) {
            final serviceStatus = await location.requestService();
            if (!serviceStatus) {
              return;
            }
          }
        }

        await Geolocator.getCurrentPosition(
            locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.low,
        ));

        final status = await FlutterQiblah.checkLocationStatus();
        if (status.enabled && status.status == LocationPermission.denied) {
          await FlutterQiblah.requestPermissions();
          final s = await FlutterQiblah.checkLocationStatus();
          locationStatus.value = s;
        } else {
          locationStatus.value = status;
        }
      }

      if (currentIndex == 4) {
        _checkLocationStatus();
      }
      return () {};
    }, []);

    final enableLocationWidget = Center(
      child: FittedBox(
        fit: BoxFit.scaleDown,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "من فضلك قم بتفعيل خدمات الموقع !",
              style: AppTextStyles.labelLarge.copyWith(
                color: ColorManager.secondaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            AppGaps.gap16,
            const QiblaPlaceHolderWidget()
          ],
        ),
      ),
    );

    if (locationStatus.value == null) {
      return const QiblaPlaceHolderWidget();
    }

    if (locationStatus.value!.enabled == true) {
      switch (locationStatus.value!.status) {
        case LocationPermission.always:
        case LocationPermission.whileInUse:
          return const QiblahCompassWidget();

        case LocationPermission.denied:
          return enableLocationWidget;
        case LocationPermission.deniedForever:
          return enableLocationWidget;

        default:
          return enableLocationWidget;
      }
    } else {
      return enableLocationWidget;
    }
  }
}

//class QiblahCompass extends StatefulWidget {
//   const QiblahCompass({super.key});
//
//   @override
//   QiblahCompassState createState() => QiblahCompassState();
// }
//
// class QiblahCompassState extends State<QiblahCompass> {
//   final _locationStreamController =
//       StreamController<LocationStatus>.broadcast();
//
//   Stream<LocationStatus> get stream => _locationStreamController.stream;
//
//   @override
//   void initState() {
//     super.initState();
//     _checkLocationStatus();
//   }
//
//   @override
//   void didChangeDependencies() {
//     // TODO: implement didChangeDependencies
//     super.didChangeDependencies();
//
//     _checkLocationStatus();
//   }
//
//   @override
//   void dispose() {
//     _locationStreamController.close();
//     FlutterQiblah().dispose();
//     super.dispose();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     final enableLocationWidget = Center(
//       child: FittedBox(
//         fit: BoxFit.scaleDown,
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Text(
//               "من فضلك قم بتفعيل خدمات الموقع !",
//               style: AppTextStyles.labelLarge.copyWith(
//                 color: ColorManager.secondaryColor,
//                 fontWeight: FontWeight.bold,
//               ),
//             ),
//             AppGaps.gap16,
//             const QiblaPlaceHolderWidget()
//           ],
//         ),
//       ),
//     );
//     return StreamBuilder(
//       stream: stream,
//       builder: (context, AsyncSnapshot<LocationStatus> snapshot) {
//         if (snapshot.connectionState == ConnectionState.waiting) {
//           return const QiblaPlaceHolderWidget();
//         }
//         if (snapshot.data!.enabled == true) {
//           switch (snapshot.data!.status) {
//             case LocationPermission.always:
//             case LocationPermission.whileInUse:
//               return const QiblahCompassWidget();
//
//             case LocationPermission.denied:
//               return enableLocationWidget;
//             case LocationPermission.deniedForever:
//               return enableLocationWidget;
//
//             default:
//               return enableLocationWidget;
//           }
//         } else {
//           return enableLocationWidget;
//         }
//       },
//     );
//   }
//
//   Future<void> _checkLocationStatus() async {
//     await Permission.location.request();
//
//     await Geolocator.getCurrentPosition(
//         locationSettings: const LocationSettings(
//       accuracy: LocationAccuracy.low,
//     ));
//     log('asfasffassafsaf222222');
//
//     final locationStatus = await FlutterQiblah.checkLocationStatus();
//     if (locationStatus.enabled &&
//         locationStatus.status == LocationPermission.denied) {
//       await FlutterQiblah.requestPermissions();
//       final s = await FlutterQiblah.checkLocationStatus();
//       _locationStreamController.sink.add(s);
//     } else {
//       _locationStreamController.sink.add(locationStatus);
//     }
//   }
// }
