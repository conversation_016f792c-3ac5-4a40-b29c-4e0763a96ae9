import 'package:flutter/cupertino.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

class CompassWidget extends StatelessWidget {
  const CompassWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SvgPicture.asset(
      'assets/images/compass.svg',
      fit: BoxFit.contain,
      width: 450,
    );
  }
}

class NeedleWidget extends StatelessWidget {
  const NeedleWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SvgPicture.asset(
      'assets/images/needle.svg',
      fit: BoxFit.contain,
      height: 260,
      alignment: Alignment.center,
    );
  }
}

class QiblaPlaceHolderWidget extends StatelessWidget {
  const QiblaPlaceHolderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    const compassSvg = CompassWidget();
    // SvgPicture.asset('assets/images/compass.svg');
    const needleSvg = NeedleWidget();
    // SvgPicture.asset(
    //   'assets/images/needle.svg',
    //   fit: BoxFit.contain,
    //   height: 300,
    //   alignment: Alignment.center,
    // );

    return Center(
      child: FittedBox(
        fit: BoxFit.scaleDown,
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  CupertinoIcons.location_circle,
                  color: ColorManager.secondaryColor,
                ),
                AppGaps.gap4,
                Text(
                  "0 كم",
                  style: AppTextStyles.labelLarge,
                ),
              ],
            ).paddingOnly(bottom: AppSpaces.padding12),
            const Stack(
              alignment: Alignment.center,
              children: [
                compassSvg,
                needleSvg,
              ],
            ),
            const Text("0°").paddingOnly(top: AppSpaces.padding12),
          ],
        ),
      ),
    );
  }
}
