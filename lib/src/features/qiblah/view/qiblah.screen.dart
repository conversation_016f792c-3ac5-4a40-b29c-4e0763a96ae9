import 'package:flutter/material.dart';
import 'package:flutter_qiblah/flutter_qiblah.dart';
import 'package:quran_broadcast_app/src/core/shared/widgets/background_widgets/background_text_body.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/qiblah_compass.dart';

class QiblahScreen extends StatelessWidget {
  const QiblahScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final deviceSupport = FlutterQiblah.androidDeviceSensorSupport();

    return const QiblahCompass().paddingSymmetric(
      horizontal: AppSpaces.screenPadding,
    );

    return BackgroundTextBody(
      headerText: "القبلة",
      child: FutureBuilder(
        future: deviceSupport,
        builder: (_, AsyncSnapshot<bool?> snapshot) {
          return const QiblahCompass();
        },
      ),
    );
  }
}
