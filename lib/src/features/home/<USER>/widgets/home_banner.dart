import 'dart:async';

import 'package:circular_countdown_timer/circular_countdown_timer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:quran_broadcast_app/src/features/calendar/providers/calendar.providers.dart';
import 'package:quran_broadcast_app/src/features/home/<USER>/widgets/next_prayer_times.dart';
import 'package:quran_broadcast_app/src/features/main_screen/view/main.screen.dart';
import 'package:xr_helper/xr_helper.dart';

class HomeBanner extends HookConsumerWidget with WidgetsBindingObserver {
  const HomeBanner({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appLifecycleState = useAppLifecycleState();
    final previousState = useRef<AppLifecycleState?>(null);

    void smoothNavigate(BuildContext context) {
      OverlayEntry? overlayEntry;

      overlayEntry = OverlayEntry(
        builder: (context) => Positioned.fill(
          child: Container(
            color: Colors.black.withOpacity(0), // Start transparent
          )
              .animate()
              .fade(duration: 300.ms, end: 1) // Fade to black
              .then(delay: 100.ms) // Small delay before navigation
              .callback(
                  duration: 0.ms,
                  callback: (_) {
                    Navigator.of(context).pushReplacement(
                      PageRouteBuilder(
                        pageBuilder: (_, __, ___) => const MainScreen(),
                        transitionDuration:
                            Duration.zero, // No default transition
                      ),
                    );

                    Future.delayed(300.ms, () {
                      try {
                        overlayEntry?.remove(); // Remove overlay after fade-in
                      } catch (e) {
                        Log.e('Error_removing overlay: $e');
                      }
                    });
                  }),
        ),
      );

      Overlay.of(context).insert(overlayEntry);
    }

    useEffect(() {
      // Timer? periodicTimer;

      if ((previousState.value == AppLifecycleState.paused ||
              previousState.value == AppLifecycleState.inactive) &&
          appLifecycleState == AppLifecycleState.resumed) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          smoothNavigate(context);

          // Cancel the periodic timer when the app resumes
          // periodicTimer?.cancel();
          // periodicTimer = null;
        });
      }

      // if ((previousState.value == AppLifecycleState.paused ||
      //     previousState.value == AppLifecycleState.inactive)) {
      //   Log.w('1 min to Exiting app');
      // periodicTimer = Timer.periodic(
      //   const Duration(minutes: 1),
      //   (timer) {
      //     Log.f('Updating app widget...');
      //     HomeWidgetService.update(context, ref: ref);
      //   },
      // );

      // Uncomment this if you want to exit the app after 1 minute
      // Timer(const Duration(minutes: 1), () {
      //   Log.f('Exiting app...');
      //   FlutterExitApp.exitApp();
      // });
      // }

      previousState.value = appLifecycleState;

      return () {
        // Clean up the timer when the effect is disposed
        // periodicTimer?.cancel();
      };
    }, [appLifecycleState]);

    final calendarController = ref.watch(calendarControllerNotifierProvider);

    return ValueListenableBuilder<DateTime>(
      valueListenable: calendarController.currentDateNotifier,
      builder: (context, currentDate, child) {
        final currentDayData = calendarController.calendarByDate(currentDate);
        final currentMonthData =
            calendarController.monthDataByDate(currentDate);
        final currentHijriMonthByDate =
            calendarController.hijriMonthByDate(currentDate);

        const timerSize = 100.0;

        return HookBuilder(builder: (context) {
          final nextPrayerNotifier = useState<NextPrayerTime>(
              getNextPrayerTime(currentDayData.prayerTimes));
          final timerKey = useState(GlobalKey<CircularCountDownTimerState>());

          useEffect(() {
            nextPrayerNotifier.value =
                getNextPrayerTime(currentDayData.prayerTimes);
            return () {};
          }, [currentDate]);

          final isCompleted = useState(false);

          void onTimerComplete() {
            final newNextPrayer = getNextPrayerTime(currentDayData.prayerTimes);
            nextPrayerNotifier.value = newNextPrayer;

            timerKey.value = GlobalKey<CircularCountDownTimerState>();

            isCompleted.value = true;
          }

          useEffect(() {
            if (isCompleted.value) {
              Future.delayed(const Duration(seconds: 2), () {
                const MainScreen().navigateReplacement;
              });
            }
            return () {};
          }, [isCompleted.value]);

          return ValueListenableBuilder(
            valueListenable: nextPrayerNotifier,
            builder: (context, nextPrayer, child) {
              return Container(
                width: double.infinity,
                margin: const EdgeInsets.symmetric(
                    horizontal: AppSpaces.screenPadding),
                decoration: BoxDecoration(
                  color: ColorManager.white,
                  image: const DecorationImage(
                    alignment: Alignment.topCenter,
                    image: AssetImage('assets/images/banner.webp'),
                    fit: BoxFit.cover,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      spreadRadius: 0,
                      blurRadius: 10,
                      offset: const Offset(0, -1),
                    ),
                    BoxShadow(
                      color: Colors.black.withOpacity(0.7),
                      spreadRadius: 0,
                      blurRadius: 10,
                      offset: const Offset(0, 1),
                    ),
                  ],
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Stack(
                  children: [
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(24),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(AppSpaces.padding8),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Center(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.1),
                                        spreadRadius: 0,
                                        blurRadius: 10,
                                        offset: const Offset(0, -1),
                                      ),
                                    ],
                                  ),
                                  child: Text(
                                    currentDate.formatDayName,
                                    style: AppTextStyles.whiteSubHeadLine
                                        .copyWith(fontSize: 26),
                                  ),
                                ),
                                if (!currentDate.isToday())
                                  const SizedBox(
                                    height: timerSize / 2.5,
                                  ),
                                Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.1),
                                        spreadRadius: 0,
                                        blurRadius: 10,
                                        offset: const Offset(0, -1),
                                      ),
                                    ],
                                  ),
                                  child: Text.rich(
                                    textAlign: TextAlign.center,
                                    TextSpan(
                                      children: [
                                        TextSpan(
                                          text:
                                              '${currentDayData.hijriDayNumber} ',
                                          style: AppTextStyles.whiteLabelMedium
                                              .copyWith(fontSize: 22),
                                        ),
                                        TextSpan(
                                          text: currentHijriMonthByDate
                                              .hijriMonthName,
                                          style: AppTextStyles.whiteLabelMedium
                                              .copyWith(fontSize: 14),
                                        ),
                                        TextSpan(
                                          text:
                                              ' (${currentHijriMonthByDate.hijriMonthNumber}) ',
                                          style: AppTextStyles.whiteHint
                                              .copyWith(fontSize: 10),
                                        ),
                                        TextSpan(
                                          text:
                                              '${currentHijriMonthByDate.hijriYear}هـ\n',
                                          style: AppTextStyles.whiteLabelMedium,
                                        ),
                                        TextSpan(
                                          text:
                                              '${currentDayData.gregorianDayNumber} ',
                                          style: AppTextStyles.whiteLabelMedium
                                              .copyWith(fontSize: 22),
                                        ),
                                        TextSpan(
                                          text: currentMonthData
                                              .gregorianMonthName,
                                          style: AppTextStyles.whiteLabelLarge
                                              .copyWith(fontSize: 14),
                                        ),
                                        TextSpan(
                                          text:
                                              ' (${currentMonthData.gregorianMonthNumber}) ',
                                          style: AppTextStyles.whiteHint
                                              .copyWith(fontSize: 10),
                                        ),
                                        TextSpan(
                                          text:
                                              '${currentMonthData.gregorianYear}م',
                                          style: AppTextStyles.whiteLabelMedium,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          if (currentDate.isToday())
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.15),
                                          spreadRadius: 0,
                                          blurRadius: 10,
                                          offset: const Offset(0, .8),
                                        ),
                                      ],
                                    ),
                                    child: FittedBox(
                                      fit: BoxFit.scaleDown,
                                      alignment: Alignment.centerRight,
                                      child: Text(
                                        nextPrayer.name == "الشروق"
                                            ? 'الوقت المتبقى للشروق'
                                            : 'الوقت المتبقى لأذان ${nextPrayer.name}',
                                        style: AppTextStyles.whiteSubTitle
                                            .copyWith(fontSize: 18),
                                      ),
                                    ),
                                  ),
                                ),
                                AppGaps.gap4,
                                CircularCountDownTimer(
                                  key: timerKey.value,
                                  width: timerSize,
                                  height: timerSize,
                                  duration: nextPrayer.duration,
                                  initialDuration: 0,
                                  fillColor: ColorManager.grey.withOpacity(0.8),
                                  backgroundColor: ColorManager.blueGrey,
                                  ringColor: ColorManager.grey.withOpacity(0.1),
                                  strokeWidth: 8.0,
                                  strokeCap: StrokeCap.round,
                                  textStyle: AppTextStyles.labelLarge.copyWith(
                                    color: ColorManager.primaryColor,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                  textAlign: TextAlign.center,
                                  isReverse: true,
                                  onComplete: onTimerComplete,
                                ),
                              ],
                            ).paddingOnly(left: AppSpaces.padding4)
                          else
                            const SizedBox(
                              height: timerSize / 1.67,
                            ),
                          AppGaps.gap4,
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        });
      },
    );
  }
}
