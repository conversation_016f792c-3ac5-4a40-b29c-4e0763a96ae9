import 'dart:developer';

import 'package:quran_broadcast_app/src/features/calendar/models/calendar_model.dart';

class NextPrayerTime {
  final int duration;
  final String name;
  final DateTime time;

  NextPrayerTime({
    required this.duration,
    required this.name,
    required this.time,
  });
}

NextPrayerTime getNextPrayerTime(
  PrayerTimeModel prayerTimes, {
  DateTime? date,
}) {
  final now = date ?? DateTime.now();

  final times = <Map<String, dynamic>>[
    {'name': 'الفجر', 'time': prayerTimes.fajr},
    {'name': 'الشروق', 'time': prayerTimes.sunrise},
    {'name': 'الظهر', 'time': prayerTimes.dhuhr},
    {'name': 'العصر', 'time': prayerTimes.asr},
    {'name': 'المغرب', 'time': prayerTimes.maghrib},
    {'name': 'العشاء', 'time': prayerTimes.isha},
  ]
      .map((prayer) {
        try {
          final timeParts = prayer['time']!.split(':');
          final hour = int.parse(timeParts[0]);
          final minute = int.parse(timeParts[1]);
          return {
            'name': prayer['name'],
            'time': DateTime(now.year, now.month, now.day, hour, minute)
          };
        } catch (e) {
          log('Error parsing time: $prayer');
          return null;
        }
      })
      .where((prayer) => prayer != null)
      .toList();

  for (var prayer in times) {
    final time = prayer!['time'] as DateTime;
    if (time.isAfter(now)) {
      final duration = time.difference(now).inSeconds;
      return NextPrayerTime(
        duration: duration,
        name: prayer['name'],
        time: time,
      );
    }
  }

  final nextDayFajr = times.firstOrNull?['time']?.add(const Duration(days: 1));
  final duration =
      nextDayFajr == null ? 0 : nextDayFajr.difference(now).inSeconds;

  return NextPrayerTime(
    duration: duration,
    name: 'الفجر',
    time: nextDayFajr ?? DateTime.now(),
  );
}

// get previousPrayerTime
NextPrayerTime getPreviousPrayerTime(PrayerTimeModel prayerTimes) {
  final now = DateTime.now();

  final times = <Map<String, dynamic>>[
    {'name': 'الفجر', 'time': prayerTimes.fajr},
    {'name': 'الشروق', 'time': prayerTimes.sunrise},
    {'name': 'الظهر', 'time': prayerTimes.dhuhr},
    {'name': 'العصر', 'time': prayerTimes.asr},
    {'name': 'المغرب', 'time': prayerTimes.maghrib},
    {'name': 'العشاء', 'time': prayerTimes.isha},
  ]
      .map((prayer) {
        try {
          final timeParts = prayer['time']!.split(':');
          final hour = int.parse(timeParts[0]);
          final minute = int.parse(timeParts[1]);
          return {
            'name': prayer['name'],
            'time': DateTime(now.year, now.month, now.day, hour, minute)
          };
        } catch (e) {
          log('Error parsing time: ${prayer['time']}');
          return null;
        }
      })
      .where((prayer) => prayer != null)
      .toList();

  for (var prayer in times.reversed) {
    final time = prayer!['time'] as DateTime;
    if (time.isBefore(now)) {
      final duration = now.difference(time).inSeconds;
      return NextPrayerTime(
        duration: duration,
        name: prayer['name'],
        time: time,
      );
    }
  }

  final previousDayIsha =
      times.lastOrNull?['time']?.subtract(const Duration(days: 1));
  final duration =
      previousDayIsha == null ? 0 : now.difference(previousDayIsha).inSeconds;

  return NextPrayerTime(
    duration: duration,
    name: 'العشاء',
    time: previousDayIsha ?? DateTime.now(),
  );
}

// get nextNextPrayerTime

// NextPrayerTime getNextPrayerTime(PrayerTimes prayerTimes) {
//   final now = DateTime.now();
//
//   final times = <Map<String, dynamic>>[
//     {'name': 'الفجر', 'time': prayerTimes.fajr},
//     {'name': 'الظهر', 'time': prayerTimes.dhuhr},
//     {'name': 'العصر', 'time': prayerTimes.asr},
//     {'name': 'المغرب', 'time': prayerTimes.maghrib},
//     {'name': 'العشاء', 'time': prayerTimes.isha},
//   ]
//       .map((prayer) {
//         try {
//           final timeParts = prayer['time']!.split(':');
//           final hour = int.parse(timeParts[0]);
//           final minute = int.parse(timeParts[1]);
//           return {
//             'name': prayer['name'],
//             'time': DateTime(now.year, now.month, now.day, hour, minute)
//           };
//         } catch (e) {
//           log('Error parsing time: ${prayer['time']}');
//           return null;
//         }
//       })
//       .where((prayer) => prayer != null)
//       .toList();
//
//   for (var prayer in times) {
//     final time = prayer!['time'] as DateTime;
//     if (time.isAfter(now)) {
//       final duration = time.difference(now).inSeconds;
//       return NextPrayerTime(
//         duration: duration,
//         name: prayer['name'],
//         time: time,
//       );
//     }
//   }
//
//   final nextDayFajr = times.firstOrNull?['time']?.add(const Duration(days: 1));
//   final duration =
//       nextDayFajr == null ? 0 : nextDayFajr.difference(now).inSeconds;
//
//   return NextPrayerTime(
//     duration: duration,
//     name: 'الفجر',
//     time: nextDayFajr,
//   );
// }
