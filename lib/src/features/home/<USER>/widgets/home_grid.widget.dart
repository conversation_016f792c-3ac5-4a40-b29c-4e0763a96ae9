import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quran_broadcast_app/src/core/shared/widgets/navigations/controller/bottom_nav_bar.controller.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:quran_broadcast_app/src/features/calendar/providers/calendar.providers.dart';
import 'package:xr_helper/xr_helper.dart';

class HomeGridWidget extends ConsumerWidget {
  const HomeGridWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final bottomNavCtrl = ref.read(bottomNavControllerProvider);
    final calendarController = ref.watch(calendarControllerNotifierProvider);

    final homeGrid = [
      "القبلة",
      "حكمة اليوم",
      "الدهري",
      "من نحن",
    ];

    final homeGridCupertinoIcons = [
      CupertinoIcons.location,
      CupertinoIcons.lightbulb,
      CupertinoIcons.calendar_today,
      CupertinoIcons.info,
    ];

    void onTap(int index) {
      if (index == 0) {
        bottomNavCtrl.changeIndex(4);
      }
      if (index == 1) {
        calendarController.currentDateNotifier.value = DateTime.now();

        bottomNavCtrl.changeIndex(5);
      }
      if (index == 2) {
        bottomNavCtrl.changeIndex(6);
      }
      if (index == 3) {
        bottomNavCtrl.changeIndex(7);
      }
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
          homeGrid.length,
          (index) => Expanded(
                child: GestureDetector(
                  onTap: () => onTap(index),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: AppSpaces.screenPadding),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircleAvatar(
                          maxRadius: 30,
                          backgroundColor: ColorManager.lightPrimaryColor2,
                          child: Icon(
                            homeGridCupertinoIcons[index],
                            color: ColorManager.primaryColor,
                            size: index == 0 || index == 1 ? 22 : 24,
                          ),
                        ),
                        AppGaps.gap8,
                        FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Text(
                            homeGrid[index],
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              fontFamily: 'Alex',
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              )),
    );
  }
}
