import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quran_broadcast_app/src/features/home/<USER>/widgets/home_banner.dart';
import 'package:quran_broadcast_app/src/features/prayer_times/view/prayer_times.widget.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/home_grid.widget.dart';

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ListView(
      padding: const EdgeInsets.only(
        top: AppSpaces.padding4,
        bottom: AppSpaces.padding12,
      ),
      children: const [
        // * Home Banner
        HomeBanner(),

        // AppGaps.gap8,

        // * Prayer Times
        PrayerTimesWidget(),

        // AppGaps.gap8,

        // * Home Content
        HomeGridWidget(),
      ],
    );
  }
}
