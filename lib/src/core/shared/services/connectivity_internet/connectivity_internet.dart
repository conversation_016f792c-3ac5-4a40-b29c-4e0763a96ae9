import 'dart:async';
import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

enum NetworkStatus { Online, Offline }

class NetworkStatusService {
  final StreamController<NetworkStatus> networkStatusController =
      StreamController<NetworkStatus>();
  late StreamSubscription<InternetConnectionStatus> _subscription;
  final connectionChecker = InternetConnectionChecker.instance;

  NetworkStatusService() {
    _subscription = connectionChecker.onStatusChange.listen((status) {
      Log.w('Network_Status: $status');

      if (status == InternetConnectionStatus.connected) {
        networkStatusController.add(NetworkStatus.Online);
      } else {
        networkStatusController.add(NetworkStatus.Offline);
      }
    });
  }

  void dispose() {
    _subscription.cancel();
    networkStatusController.close();
  }
}

class NetworkAwareWidget extends StatelessWidget {
  final Widget onlineChild;
  final Widget offlineChild;

  const NetworkAwareWidget({
    super.key,
    required this.onlineChild,
    required this.offlineChild,
  });

  @override
  Widget build(BuildContext context) {
    if (Platform.isIOS) {
      return NetworkAwareWidgetIOS(
        onlineChild: onlineChild,
        offlineChild: offlineChild,
      );
    }
    NetworkStatus networkStatus = Provider.of<NetworkStatus>(context);
    return networkStatus == NetworkStatus.Online ? onlineChild : offlineChild;
  }
}

enum NetworkStatusIOS { Online, Offline }

class NetworkStatusIOSService {
  StreamController<NetworkStatusIOS> networkStatusController =
      StreamController<NetworkStatusIOS>();

  NetworkStatusIOSService() {
    Connectivity().onConnectivityChanged.listen((status) async {
      networkStatusController.add(await _getNetworkStatusIOS(status));
    });
  }

  Future<NetworkStatusIOS> _getNetworkStatusIOS(
      List<ConnectivityResult> status) async {
    final hasInternet = await _hasInternetAccess();

    return status.contains(ConnectivityResult.mobile) ||
            status.contains(ConnectivityResult.wifi)
        ? hasInternet
            ? NetworkStatusIOS.Online
            : NetworkStatusIOS.Offline
        : NetworkStatusIOS.Offline;
  }

  Future<bool> _hasInternetAccess() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
        return true;
      }
    } on SocketException catch (_) {
      return false;
    }
    return false;
  }
}

class NetworkAwareWidgetIOS extends StatelessWidget {
  final Widget onlineChild;
  final Widget offlineChild;

  const NetworkAwareWidgetIOS(
      {super.key, required this.onlineChild, required this.offlineChild});

  @override
  Widget build(BuildContext context) {
    NetworkStatusIOS networkStatus = Provider.of<NetworkStatusIOS>(context);
    if (networkStatus == NetworkStatusIOS.Online) {
      return onlineChild;
    } else {
      return offlineChild;
    }
  }
}
