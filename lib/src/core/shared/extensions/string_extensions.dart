extension StringExtensions on String {
  //convertTo12Hour
  String get convertTo12Hour {
    if (this.isEmpty) return '';
    final parts = this.split(':');
    final hour = int.parse(parts[0]);
    final minute = parts[1];
    final period = hour >= 12 ? 'م' : 'ص';
    final hour12 = hour > 12 ? hour - 12 : hour;
    return '$hour12:$minute $period';
  }

  String get translateErrorMessage {
    //?{"email":"Client Not Found"}

    final errorMessage = this;

    if (!errorMessage.contains(':')) return errorMessage;

    final errorMessageSplitted = errorMessage.split(':');

    final errorKey =
        errorMessageSplitted[0].replaceAll('{', '').replaceAll('"', '');

    final errorValue =
        errorMessageSplitted[1].replaceAll('}', '').replaceAll('"', '');

    return '$errorKey: $errorValue';
  }

  ///capitalize
  String get capitalize {
    if (this.isEmpty) {
      return this;
    }
    return this[0].toUpperCase() + this.substring(1);
  }

  // String get dayName {
//     return DateFormat('EEEE').format(this);
//   }
  String get dayName {
    if (this!.isEmpty) return '';
    final date = DateTime.tryParse(this!) ?? DateTime.now();
    const daysInArabic = [
      'الأحد',
      'الإثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت'
    ];
    return daysInArabic[date.weekday % 7];
  }

  int get toInt {
    return int.tryParse(this) ?? 0;
  }
}
