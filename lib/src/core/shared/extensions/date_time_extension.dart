extension DateTimeExtension on DateTime {
  bool isSameDay(DateTime? other) {
    if (other == null) {
      return false;
    }
    return year == other.year && month == other.month && day == other.day;
  }

  // date format
  // String get dayName {
  //   return DateFormat('EEEE').format(this);
  // }
  String get dayName {
    const daysInArabic = [
      'الأحد',
      'الإثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت'
    ];
    return daysInArabic[weekday % 7];
  }
}
