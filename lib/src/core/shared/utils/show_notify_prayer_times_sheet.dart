import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quran_broadcast_app/src/features/prayer_times/view/widgets/notify_prayer_times_sheet.widget.dart';

void showNotifyPrayerTimesSheet(
  BuildContext context, {
  required WidgetRef ref,
}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    isDismissible: true,
    enableDrag: true,
    showDragHandle: true,
    backgroundColor: Colors.white,
    builder: (BuildContext context) {
      return const _NotifyPrayerTimesAnimated();
    },
  );
}

class _NotifyPrayerTimesAnimated extends ConsumerWidget {
  const _NotifyPrayerTimesAnimated({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      color: Colors.white,
      child: const NotifyPrayerTimesWidget(),
    );
  }
}
