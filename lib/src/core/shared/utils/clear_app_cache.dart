import 'dart:io';

import 'package:path_provider/path_provider.dart';
import 'package:xr_helper/xr_helper.dart';

Future<void> clearCache() async {
  try {
    Log.w('App_Cache_Start.');
    final cacheDir = await getTemporaryDirectory();
    if (await cacheDir.exists()) {
      await _deleteDirectory(cacheDir);
      Log.w('App_Cache_Cleared_Successfully.');
    }

    // Optional: clear external cache (Android only)
    final externalCacheDir = await getExternalCacheDirectories();
    if (externalCacheDir != null) {
      for (var dir in externalCacheDir) {
        if (await dir.exists()) {
          await _deleteDirectory(dir);
          Log.w('External_Cache_Cleared: ${dir.path}');
        }
      }
    }
  } catch (e) {
    Log.e('Failed_To_Clear_Cache: $e');
  }
}

Future<void> clearAppFiles() async {
  try {
    final appDir = await getApplicationSupportDirectory();
    if (await appDir.exists()) {
      await _deleteDirectory(appDir);
    }

    final docsDir = await getApplicationDocumentsDirectory();
    if (await docsDir.exists()) {
      await _deleteDirectory(docsDir);
    }

    Log.w('App_Files_Cleared_Successfully.');
  } catch (e) {
    Log.e("Failed_To_Clear_Files: $e");
  }
}

/// Recursive deletion function
Future<void> _deleteDirectory(Directory dir) async {
  if (!await dir.exists()) return;

  try {
    final entities = dir.listSync(recursive: true);
    for (var entity in entities) {
      if (entity is File) {
        await entity.delete();
      } else if (entity is Directory) {
        await _deleteDirectory(entity); // recurse
      }
    }
    await dir.delete(recursive: true);
  } catch (e) {
    Log.e("Error_Deleting_Directory: $e");
  }
}
