import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:xr_helper/xr_helper.dart';

ValueNotifier<bool> useBookmark(num pageNumber) {
  final isBookmark = useState(
    GetStorageService.getData(key: LocalKeys.favoritePage) == pageNumber,
  );

  useEffect(() {
    void checkBookmark() {
      isBookmark.value =
          GetStorageService.getData(key: LocalKeys.favoritePage) == pageNumber;
    }

    checkBookmark();

    final timer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      checkBookmark();
    });

    return () {
      timer.cancel();
    };
  }, [pageNumber]);

  return isBookmark;
}
