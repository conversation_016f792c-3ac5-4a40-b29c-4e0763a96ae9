import 'package:flutter/material.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

class NextPreviousWidget extends StatelessWidget {
  final Widget? centerWidget;
  final Function()? onPrevious;
  final Function()? onNext;
  final bool isCircleAvatar;
  final bool isSpaceBetween;
  final double? size;
  final double? circleAvatarRadius;

  const NextPreviousWidget({
    super.key,
    this.centerWidget,
    this.onPrevious,
    this.onNext,
    this.isCircleAvatar = true,
    this.isSpaceBetween = false,
    this.size,
    this.circleAvatarRadius,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: isSpaceBetween
          ? MainAxisAlignment.spaceBetween
          : MainAxisAlignment.center,
      children: [
        Row(
          children: [
            if (onPrevious != null)
              GestureDetector(
                onTap: onPrevious,
                child: isCircleAvatar
                    ? CircleAvatar(
                        radius: circleAvatarRadius,
                        backgroundColor: ColorManager.primaryColor,
                        child: Icon(
                          Icons.chevron_left_rounded,
                          color: Colors.white,
                          size: size,
                        ),
                      )
                    : Icon(
                        Icons.chevron_left_rounded,
                        color: ColorManager.black,
                        size: size,
                      ),
              ),
          ],
        ),
        if (centerWidget != null) centerWidget! else AppGaps.gap24,
        Row(
          children: [
            if (onNext != null)
              GestureDetector(
                onTap: onNext,
                child: isCircleAvatar
                    ? CircleAvatar(
                        radius: circleAvatarRadius,
                        backgroundColor: ColorManager.primaryColor,
                        child: Icon(
                          Icons.chevron_right_rounded,
                          color: Colors.white,
                          size: size,
                        ),
                      )
                    : Icon(
                        Icons.chevron_right_rounded,
                        color: ColorManager.black,
                        size: size,
                      ),
              ),
          ],
        ),
      ],
    );
  }
}
