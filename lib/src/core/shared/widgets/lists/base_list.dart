import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../consts/app_constants.dart';
import '../loading/loading_widget.dart';

class BaseList<T> extends HookConsumerWidget {
  final List<T> data;
  final Widget Function(T data, int index) itemBuilder;
  final Widget separatorGap;
  final bool isHorizontal;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final bool showEmptyWidget;
  final Alignment? alignment;
  final ScrollPhysics? physics;
  final Widget? emptyWidget;
  final Function()? onLoadMore;
  final bool isLoading;
  final Widget? loadingWidget;

  // * For Grid View
  final bool isGrid;
  final int crossAxisCount;
  final double crossAxisSpacing;
  final double mainAxisSpacing;

  const BaseList({
    super.key,
    required this.data,
    required this.itemBuilder,
    this.separatorGap = AppGaps.gap12,
    this.isHorizontal = false,
    this.showEmptyWidget = true,
    this.isGrid = false,
    this.height,
    this.emptyWidget,
    this.padding,
    this.physics,
    this.alignment,
    this.onLoadMore,
    this.loadingWidget,
    this.isLoading = false,
    this.crossAxisCount = 2,
    this.crossAxisSpacing = 0,
    this.mainAxisSpacing = 0,
  });

  const BaseList.horizontal({
    super.key,
    required this.data,
    required this.itemBuilder,
    this.separatorGap = AppGaps.gap8,
    this.isHorizontal = true,
    this.physics,
    this.padding,
    this.emptyWidget,
    this.showEmptyWidget = true,
    this.isLoading = false,
    this.isGrid = false,
    required this.height,
    this.alignment,
    this.loadingWidget,
    this.onLoadMore,
    this.crossAxisCount = 2,
    this.crossAxisSpacing = 0,
    this.mainAxisSpacing = 0,
  });

  const BaseList.grid({
    super.key,
    required this.data,
    required this.itemBuilder,
    this.separatorGap = AppGaps.gap8,
    this.isHorizontal = false,
    this.showEmptyWidget = true,
    this.height,
    this.emptyWidget,
    this.padding,
    this.physics,
    this.alignment,
    this.onLoadMore,
    this.loadingWidget,
    this.isLoading = false,
    this.isGrid = true,
    this.crossAxisCount = 2,
    this.crossAxisSpacing = AppSpaces.padding12,
    this.mainAxisSpacing = AppSpaces.padding8,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (isLoading && data.isEmpty) {
      return loadingWidget ?? const LoadingWidget();
    }

    if (data.isEmpty && showEmptyWidget) {
      return emptyWidget ?? const Text('لا توجد نتائج');
    }

    if (data.isEmpty) return const SizedBox.shrink();

    // if (isGrid) {
    //   return Padding(
    //     padding: padding ?? const EdgeInsets.all(AppSpaces.padding8),
    //     child: StaggeredGrid.count(
    //       crossAxisCount: crossAxisCount,
    //       crossAxisSpacing: crossAxisSpacing,
    //       mainAxisSpacing: mainAxisSpacing,
    //       children: data.map((e) => itemBuilder(e, data.indexOf(e))).toList(),
    //     ),
    //   );
    // }

    Widget baseList({
      bool isHorizontal = false,
    }) {
      // if (onLoadMore != null) {
      //   return PaginatedList<T>(
      //     loadingIndicator: const LoadingWidget(),
      //     items: data,
      //     isRecentSearch: false,
      //     shrinkWrap: true,
      //     isLastPage: CursorProvider().lastPaginate,
      //     onLoadMore: (index) => onLoadMore!(),
      //     builder: itemBuilder,
      //   );
      // }

      return ListView.separated(
        padding: padding,
        shrinkWrap: true,
        physics: physics,
        itemCount: data.length,
        scrollDirection: isHorizontal ? Axis.horizontal : Axis.vertical,
        itemBuilder: (context, index) {
          return itemBuilder(data[index], index);
        },
        separatorBuilder: (context, index) => separatorGap,
      );
    }

    if (isHorizontal) {
      return baseList(isHorizontal: true).decorated(
        height: height,
        alignment: alignment ??
            (AppConsts.isEnglish
                ? Alignment.centerLeft
                : Alignment.centerRight),
      );
    }

    return baseList();
  }
}
