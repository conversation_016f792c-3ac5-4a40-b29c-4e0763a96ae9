import 'package:flutter/material.dart';
import 'package:quran_broadcast_app/generated/assets.gen.dart';

class BackgroundBody extends StatelessWidget {
  final Widget child;

  const BackgroundBody({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Opacity(
          opacity: 0.4,
          child: Assets.images.background.image(
            height: double.infinity,
            width: double.infinity,
            fit: BoxFit.cover,
          ),
        ),
        child,
      ],
    );
  }
}
