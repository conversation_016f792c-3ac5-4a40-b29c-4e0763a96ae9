import 'package:flutter/material.dart';
import 'package:quran_broadcast_app/generated/assets.gen.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

class BackgroundText extends StatelessWidget {
  final String text;
  const BackgroundText({super.key, required this.text});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 220,
      height: 200,
      decoration: BoxDecoration(
        image: DecorationImage(
          image: Assets.images.textBackground.provider(),
          fit: BoxFit.cover, 
        ),
      ),
      alignment: Alignment.center,
      child: Text(
        text,
        style: AppTextStyles.subHeadLine.copyWith(
          color: ColorManager.primaryColor,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
