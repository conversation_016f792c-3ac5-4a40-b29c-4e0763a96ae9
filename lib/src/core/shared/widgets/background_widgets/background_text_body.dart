import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:quran_broadcast_app/generated/assets.gen.dart';
import 'package:quran_broadcast_app/src/core/shared/widgets/background_widgets/background_body.dart';
import 'package:quran_broadcast_app/src/core/shared/widgets/background_widgets/background_text.dart';
import 'package:xr_helper/xr_helper.dart';

class BackgroundTextBody extends StatelessWidget {
  final String headerText;
  final Widget child;
  final double padding;
  final Widget? topSectionWidget;

  const BackgroundTextBody({
    super.key,
    required this.headerText,
    required this.child,
    this.padding = 30,
    this.topSectionWidget,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // bottomNavigationBar: const BottomNavBarWidget(),
      body: BackgroundBody(
        child: Stack(
          children: [
            Assets.images.header.image(
              height: 280,
              width: double.infinity,
              fit: BoxFit.cover,
            ),
            Positioned(
              top: 40,
              right: 20,
              child: CircleAvatar(
                backgroundColor: Colors.white,
                radius: 20,
                child: IconButton(
                  icon: const Icon(CupertinoIcons.back),
                  onPressed: () {
                    navService.back();
                  },
                ),
              ),
            ),
            if (topSectionWidget != null)
              Positioned(
                top: 40,
                left: 20,
                child: topSectionWidget!,
              ),
            Align(
              alignment: Alignment.topCenter,
              child: Padding(
                padding: const EdgeInsets.only(top: AppSpaces.padding80),
                child: BackgroundText(text: headerText),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                top: AppSpaces.topPadding + padding,
                right: AppSpaces.screenPadding,
                left: AppSpaces.screenPadding,
              ),
              child: child,
            ),
          ],
        ),
      ),
    );
  }
}
