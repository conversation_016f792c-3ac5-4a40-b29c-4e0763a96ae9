import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:xr_helper/xr_helper.dart';

class SalomonBottomBar extends StatelessWidget {
  SalomonBottomBar({
    Key? key,
    required this.items,
    this.backgroundColor,
    this.currentIndex = 0,
    this.onTap,
    this.selectedItemColor,
    this.unselectedItemColor,
    this.selectedColorOpacity,
    this.itemShape = const StadiumBorder(),
    this.margin = const EdgeInsets.all(8),
    this.itemPadding = const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
    this.duration = const Duration(milliseconds: 500),
    this.curve = Curves.easeOutQuint,
  }) : super(key: key);

  final List<SalomonBottomBarItem> items;
  final int currentIndex;
  final Function(int)? onTap;
  final Color? backgroundColor;
  final Color? selectedItemColor;
  final Color? unselectedItemColor;
  final double? selectedColorOpacity;
  final ShapeBorder itemShape;
  final EdgeInsets margin;
  final EdgeInsets itemPadding;
  final Duration duration;
  final Curve curve;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return ColoredBox(
      color: backgroundColor ?? Colors.transparent,
      child: SafeArea(
        minimum: margin,
        child: FittedBox(
          fit: BoxFit.scaleDown,
          child: Row(
            mainAxisAlignment: items.length <= 2
                ? MainAxisAlignment.spaceEvenly
                : MainAxisAlignment.spaceBetween,
            children: [
              for (final item in items)
                TweenAnimationBuilder<double>(
                  tween: Tween(
                    end: items.indexOf(item) == currentIndex ? 1.0 : 0.0,
                  ),
                  curve: curve,
                  duration: duration,
                  builder: (context, t, _) {
                    final _selectedColor = item.selectedColor ??
                        selectedItemColor ??
                        theme.primaryColor;

                    final _unselectedColor = item.unselectedColor ??
                        unselectedItemColor ??
                        theme.iconTheme.color;

                    return Material(
                      color: Color.lerp(
                          _selectedColor.withOpacity(0.0),
                          _selectedColor
                              .withOpacity(selectedColorOpacity ?? 0.1),
                          t),
                      shape: itemShape,
                      child: InkWell(
                        onTap: () => onTap?.call(items.indexOf(item)),
                        customBorder: itemShape,
                        focusColor: _selectedColor.withOpacity(0.1),
                        highlightColor: _selectedColor.withOpacity(0.1),
                        splashColor: _selectedColor.withOpacity(0.1),
                        hoverColor: _selectedColor.withOpacity(0.1),
                        child: Padding(
                          padding: itemPadding,
                          child: Row(
                            children: [
                              IconTheme(
                                data: IconThemeData(
                                  color: Color.lerp(
                                      _unselectedColor, _selectedColor, t),
                                  size: items.indexOf(item) == currentIndex
                                      ? 19
                                      : 17,
                                ),
                                child: items.indexOf(item) == currentIndex
                                    ? item.activeIcon ?? item.icon
                                    : item.icon,
                              ),
                              Padding(
                                padding: const EdgeInsets.only(right: 4.0),
                                child: DefaultTextStyle(
                                  style: AppTextStyles.labelLarge.copyWith(
                                    color: items.indexOf(item) == currentIndex
                                        ? _selectedColor
                                        : _unselectedColor,
                                    fontFamily:
                                        GoogleFonts.alexandria().fontFamily,
                                    fontSize:
                                        items.indexOf(item) == currentIndex
                                            ? currentIndex == 2
                                                ? 11
                                                : 12
                                            : 10,
                                  ),

                                  // TextStyle(
                                  //   color: items.indexOf(item) == currentIndex
                                  //       ? _selectedColor
                                  //       : _unselectedColor,
                                  //   fontWeight: FontWeight.w600,
                                  //   fontSize: items.indexOf(item) == currentIndex
                                  //       ? 16
                                  //       : 12,
                                  // ),
                                  child: item.title,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
            ],
          ),
        ),
      ),
    );
  }
}

class SalomonBottomBarItem {
  final Widget icon;
  final Widget? activeIcon;
  final Widget title;
  final Color? selectedColor;
  final Color? unselectedColor;

  SalomonBottomBarItem({
    required this.icon,
    required this.title,
    this.selectedColor,
    this.unselectedColor,
    this.activeIcon,
  });
}
