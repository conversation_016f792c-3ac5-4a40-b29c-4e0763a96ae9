import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quran_broadcast_app/generated/assets.gen.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:quran_broadcast_app/src/features/calendar/providers/calendar.providers.dart';

import 'controller/bottom_nav_bar.controller.dart';
import 'widgets/base_bottom_bar.dart';

class BottomNavBarWidget extends ConsumerWidget {
  const BottomNavBarWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(bottomNavigationControllerProvider);
    final calendarController = ref.watch(calendarControllerNotifierProvider);
    final bottomNavCtrl = ref.read(bottomNavControllerProvider);

    final isHomeGrid = currentIndex == 4 ||
        currentIndex == 5 ||
        currentIndex == 6 ||
        currentIndex == 7;

    return ClipRRect(
      child: Container(
        margin: const EdgeInsets.only(
          bottom: 20,
          left: 8,
          right: 8,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(100),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              spreadRadius: 5,
              blurRadius: 7,
              offset: const Offset(0, -1),
            ),
          ],
        ),
        child: SalomonBottomBar(
          backgroundColor: Colors.transparent,
          itemPadding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 10,
          ),
          currentIndex: isHomeGrid ? 0 : currentIndex,
          onTap: (index) {
            if (index == 0) {
              calendarController.currentDateNotifier.value = DateTime.now();

              bottomNavCtrl.changeIndex(0);

              return;
            }

            bottomNavCtrl.changeIndex(index);

            // const MainScreen().navigateReplacement;
          },
          items: [
            /// Home
            SalomonBottomBarItem(
              selectedColor: ColorManager.primaryColor,
              icon: const Icon(CupertinoIcons.house),
              title: const Text(
                "الرئيسية",
                style: TextStyle(
                  fontFamily: 'Alex',
                ),
              ),
            ),

            SalomonBottomBarItem(
              icon: Assets.icons.live.image(
                width: 22,
                color: currentIndex == 1
                    ? ColorManager.primaryColor
                    : ColorManager.black,
              ),
              title: const Text("استمع للبث",
                  style: TextStyle(
                    fontFamily: 'Alex',
                  )),
              selectedColor: ColorManager.primaryColor,
            ),

            /// Search
            SalomonBottomBarItem(
              // icon: const Icon(Icons.book),
              icon: const Icon(
                CupertinoIcons.book,
              ),
              title: const Text("القرآن الكريم",
                  style: TextStyle(
                    fontFamily: 'Alex',
                  )),
              selectedColor: ColorManager.primaryColor,
            ),

            /// Profile
            SalomonBottomBarItem(
              icon: const Icon(
                CupertinoIcons.settings,
              ),
              title: const Text("الإعدادات",
                  style: TextStyle(
                    fontFamily: 'Alex',
                  )),
              selectedColor: ColorManager.primaryColor,
            ),
          ],
        ),
      ),
    );
  }
}
