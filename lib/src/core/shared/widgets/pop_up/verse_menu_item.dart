import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class VerseMenuItem extends StatelessWidget {
  const VerseMenuItem(
      {super.key, required this.iconPath, required this.buttonName});
  final IconData iconPath;
  final String buttonName;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 120,
      height: 45,
      child: Row(
        children: [
          Expanded(
            flex: 1,
            child: Icon(
              iconPath,
              color: Colors.white,
              size: 20,
            ),
            // child: SvgPicture.asset(iconPath,
            //     color: Colors.white, width: 20, height: 20),
          ),
          AppGaps.gap12,
          Expanded(
            flex: 3,
            child: Text(
              buttonName,
              style: AppTextStyles.whiteSubTitle,
            ),
          )
        ],
      ),
    );
  }
}
