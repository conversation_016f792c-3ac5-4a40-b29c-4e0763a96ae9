// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:quran_broadcast_app/src/core/shared/widgets/pop_up/verse_menu_item.dart';
// import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
//
// /// CustomGestureDetector takes globalKey to identify child
// class VersePopUpMenu extends StatelessWidget {
//   const VersePopUpMenu({
//     super.key,
//     required this.child,
//     required this.globalKey,
//     required this.verseModel,
//     required this.playFunction,
//     required this.favoriteFunction,
//     required this.bookmarkFunction,
//     required this.shareFunction,
//     this.isPlaying = false,
//     this.isFavorite = false,
//     this.isBookmark = false,
//     required this.changeSelectedVerseKey,
//   });
//   final Widget child;
//   final GlobalKey globalKey;
//   final VerseModel verseModel;
//   final bool isPlaying;
//   final bool isFavorite;
//   final bool isBookmark;
//   final Function(VerseModel verseModel, bool isPlaying) playFunction;
//   final Function(VerseModel verseModel, bool isFavorite) favoriteFunction;
//   final Function(
//           EBookMarkType bookMarkType, VerseModel verseModel, bool isBookmark)
//       bookmarkFunction;
//   final Function(VerseModel) shareFunction;
//   final Function(String? selectedVerseKey) changeSelectedVerseKey;
//
//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onLongPress: () async {
//         changeSelectedVerseKey(verseModel.verseKey);
//         RenderBox box =
//             globalKey.currentContext?.findRenderObject() as RenderBox;
//         Offset position = box.localToGlobal(Offset.zero);
//         await showMenu(
//           context: context,
//           color: ColorManager.containerColor,
//           shape: RoundedRectangleBorder(
//             side: BorderSide(
//               color: Colors.white.withOpacity(0.1),
//             ),
//             borderRadius: BorderRadius.circular(10),
//           ),
//           position: RelativeRect.fromRect(
//             Rect.fromLTWH(
//                 position.dx + kSizeM,
//                 position.dy + (box.paintBounds.size.height - 200 - kSizeM),
//                 0,
//                 0),
//             Rect.fromLTWH(
//                 0, 0, box.paintBounds.size.width, box.paintBounds.size.height),
//           ),
//           items: [
//             PopupMenuItem(
//               onTap: () => playFunction(verseModel, isPlaying),
//               child: VerseMenuItem(
//                 iconPath:
//                     isPlaying ? CupertinoIcons.pause : CupertinoIcons.play,
//                 buttonName: isPlaying ? "إيقاف" : "تشغيل",
//               ),
//             ),
//             // PopupMenuItem(
//             //   onTap: () => favoriteFunction(verseModel, isFavorite),
//             //   child: VerseMenuItem(
//             //     iconPath: isFavorite
//             //         ?CupertinoIcons.hand_thumbsup_fill
//             //         : CupertinoIcons.hand_thumbsup,
//             //     buttonName: "المفضلة",
//             //   ),
//             // ),
//             PopupMenuItem(
//               // onTap: () =>
//               //     bookmarkFunction(EBookMarkType.verse, verseModel, isBookmark),
//               child: VerseMenuItem(
//                 iconPath: isBookmark
//                     ? CupertinoIcons.bookmark_fill
//                     : CupertinoIcons.bookmark,
//                 buttonName: "العلامة",
//               ),
//             ),
//             PopupMenuItem(
//               onTap: () => shareFunction(verseModel),
//               child: VerseMenuItem(
//                 iconPath: CupertinoIcons.share,
//                 buttonName: "مشاركة",
//               ),
//             ),
//           ],
//         );
//         changeSelectedVerseKey(null);
//       },
//       child: child,
//     );
//   }
// }
