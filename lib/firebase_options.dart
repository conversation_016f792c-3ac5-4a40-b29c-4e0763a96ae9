// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDZ5sYyJ7liY4_6sJnAaetSVGGYXpC_ZmQ',
    appId: '1:1094911823430:web:e370993ee895d57f860447',
    messagingSenderId: '1094911823430',
    projectId: 'quran-nables-app',
    authDomain: 'quran-nables-app.firebaseapp.com',
    storageBucket: 'quran-nables-app.firebasestorage.app',
    measurementId: 'G-KTZKRZ3RE7',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCk0H_7P1HmpepccnO_GUig-FYg2EZ-a8E',
    appId: '1:1094911823430:android:33616401b2d97e42860447',
    messagingSenderId: '1094911823430',
    projectId: 'quran-nables-app',
    storageBucket: 'quran-nables-app.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCp0ZkxqLpGM7X-FQE2oYOIE65bpVPz0qU',
    appId: '1:1094911823430:ios:6a35db9020a80348860447',
    messagingSenderId: '1094911823430',
    projectId: 'quran-nables-app',
    storageBucket: 'quran-nables-app.firebasestorage.app',
    iosBundleId: 'com.perfectfit.QuranKareem2',
  );

}