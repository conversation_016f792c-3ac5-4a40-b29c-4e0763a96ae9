<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>com.transistorsoft.fetch</string>
		<string>com.transistorsoft.update</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>تقويم إذاعة القرآن الكريم</string>
	<key>MinimumOSVersion</key>
    <string>14.0</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>quran_broadcast_app</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsArbitraryLoadsForMedia</key>
		<true/>
	  <key>FirebaseAppDelegateProxyEnabled</key>
  <string>false</string>
  <key>NSPushNotificationsUsageDescription</key>
  <string>We need permission to send you important updates.</string>
  <key>NSUserNotificationUsageDescription</key>
  <string>We need your permission to send you notifications about important updates.</string>
</dict>
	<key>Permitted background tdask scheduler identifiers</key>
	<string>com.transistorsoft.fetch</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
		<string>remote-notification</string>
		<string>fetch</string>
		<string>processing</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>

	<!-- ✅ Privacy Usage Descriptions -->
	<key>NSMicrophoneUsageDescription</key>
	<string>نستخدم الميكروفون لتحسين تجربة المستخدم في بعض الميزات الصوتية داخل التطبيق.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>يتم استخدام الموقع لحساب اتجاه القبلة بدقة حتى أثناء تشغيل التطبيق في الخلفية.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>نحتاج إلى موقعك الحالي لتحديد اتجاه القبلة بدقة عند استخدام التطبيق.</string>
</dict>
</plist>
