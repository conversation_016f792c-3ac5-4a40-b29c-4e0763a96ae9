PODS:
  - audio_service (0.0.1):
    - Flutter
  - audio_session (0.0.1):
    - Flutter
  - awesome_notifications (0.10.0):
    - Flutter
    - IosAwnCore (~> 0.10.0)
  - background_fetch (1.3.7):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - eraser (0.0.1):
    - Flutter
  - Firebase/CoreOnly (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - Firebase/Messaging (11.10.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.10.0)
  - firebase_core (3.13.1):
    - Firebase/CoreOnly (= 11.10.0)
    - Flutter
  - firebase_messaging (15.2.6):
    - Firebase/Messaging (= 11.10.0)
    - firebase_core
    - Flutter
  - FirebaseCore (11.10.0):
    - FirebaseCoreInternal (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreInternal (11.10.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseInstallations (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - Flutter (1.0.0)
  - flutter_compass_v2 (0.0.1):
    - Flutter
  - flutter_exit_app (1.1.4):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_native_splash (2.4.3):
    - Flutter
  - flutter_qiblah (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - home_widget (0.0.1):
    - Flutter
  - IosAwnCore (0.10.0)
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - location (0.0.1):
    - Flutter
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - wakelock_plus (0.0.1):
    - Flutter

DEPENDENCIES:
  - audio_service (from `.symlinks/plugins/audio_service/ios`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - awesome_notifications (from `.symlinks/plugins/awesome_notifications/ios`)
  - background_fetch (from `.symlinks/plugins/background_fetch/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - eraser (from `.symlinks/plugins/eraser/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_compass_v2 (from `.symlinks/plugins/flutter_compass_v2/ios`)
  - flutter_exit_app (from `.symlinks/plugins/flutter_exit_app/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_qiblah (from `.symlinks/plugins/flutter_qiblah/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - home_widget (from `.symlinks/plugins/home_widget/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - location (from `.symlinks/plugins/location/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleUtilities
    - IosAwnCore
    - nanopb
    - PromisesObjC

EXTERNAL SOURCES:
  audio_service:
    :path: ".symlinks/plugins/audio_service/ios"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  awesome_notifications:
    :path: ".symlinks/plugins/awesome_notifications/ios"
  background_fetch:
    :path: ".symlinks/plugins/background_fetch/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  eraser:
    :path: ".symlinks/plugins/eraser/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_compass_v2:
    :path: ".symlinks/plugins/flutter_compass_v2/ios"
  flutter_exit_app:
    :path: ".symlinks/plugins/flutter_exit_app/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_qiblah:
    :path: ".symlinks/plugins/flutter_qiblah/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  home_widget:
    :path: ".symlinks/plugins/home_widget/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  location:
    :path: ".symlinks/plugins/location/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"

SPEC CHECKSUMS:
  audio_service: f509d65da41b9521a61f1c404dd58651f265a567
  audio_session: 19e9480dbdd4e5f6c4543826b2e8b0e4ab6145fe
  awesome_notifications: dd5518ff1c80be03d4f1c40f04da9d9cc2a37af5
  background_fetch: 39f11371c0dce04b001c4bfd5e782bcccb0a85e2
  connectivity_plus: 2a701ffec2c0ae28a48cf7540e279787e77c447d
  eraser: 23d701c0ff53a9d042581459b752fd76a6ad6ca4
  Firebase: 1fe1c0a7d9aaea32efe01fbea5f0ebd8d70e53a2
  firebase_core: 3c2f323cae65c97a636a05a23b17730ef93df2cf
  firebase_messaging: 456e01ff29a451c90097d0b45925551d5be0c143
  FirebaseCore: 8344daef5e2661eb004b177488d6f9f0f24251b7
  FirebaseCoreInternal: ef4505d2afb1d0ebbc33162cb3795382904b5679
  FirebaseInstallations: 9980995bdd06ec8081dfb6ab364162bdd64245c3
  FirebaseMessaging: 2b9f56aa4ed286e1f0ce2ee1d413aabb8f9f5cb9
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_compass_v2: f7704b078814b705e76ae2bd48fe48ae6cd067df
  flutter_exit_app: 452eaaeeda718095eeebcb45c9fe2e7e37f212b4
  flutter_local_notifications: ff50f8405aaa0ccdc7dcfb9022ca192e8ad9688f
  flutter_native_splash: f71420956eb811e6d310720fee915f1d42852e7a
  flutter_qiblah: f69eeb708d127a2634073ab1f27771c979928f50
  fluttertoast: 21eecd6935e7064cc1fcb733a4c5a428f3f24f0f
  geolocator_apple: 9bcea1918ff7f0062d98345d238ae12718acfbc1
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  home_widget: 0434835a4c9a75704264feff6be17ea40e0f0d57
  IosAwnCore: 653786a911089012092ce831f2945cd339855a89
  just_audio: a42c63806f16995daf5b219ae1d679deb76e6a79
  location: d5cf8598915965547c3f36761ae9cc4f4e87d22e
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  share_plus: 8b6f8b3447e494cca5317c8c3073de39b3600d1f
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  wakelock_plus: 373cfe59b235a6dd5837d0fb88791d2f13a90d56

PODFILE CHECKSUM: ba91c9a4ea1a5d12f060ce077ff857f9875b3dea

COCOAPODS: 1.16.2
