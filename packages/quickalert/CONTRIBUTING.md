<img src="https://raw.githubusercontent.com/belovance/QuickAlert/master/screenshots/demo.gif?raw=true" alt="QuickAlert Demo" width="100%">

# Contributing to QuickAlert

Welcome to QuickAlert, a versatile and user-friendly Flutter package for creating beautiful and customizable alerts! Your contributions help make QuickAlert even better. Whether you're reporting bugs, suggesting enhancements, or contributing code, we appreciate your effort.

## Ways to Contribute

There are several ways you can contribute to QuickAlert:

1. **Reporting Bugs:** If you encounter a bug or unexpected behavior, please [open an issue](https://github.com/belovance/QuickAlert/issues/new) with a detailed description and steps to reproduce it.

2. **Suggesting Enhancements:** Have a brilliant idea for a new feature or improvement? [Open an issue](https://github.com/belovance/QuickAlert/issues/new) and share your thoughts. We value your creativity!

3. **Code Contributions:** If you're a developer, you can directly contribute code to QuickAlert:
   - Fork the repository.
   - Create a new branch with a descriptive name (`feature/new-feature` or `bugfix/fix-issue`).
   - Write your code and commit changes.
   - Push your changes to your fork.
   - Create a pull request (PR) from your fork's branch to the main repository's `main` branch.

## Development Setup

To set up QuickAlert for local development, follow these steps:

1. **Fork the Repository:** Click the "Fork" button on the [QuickAlert GitHub repository](https://github.com/belovance/QuickAlert) to create a copy in your GitHub account.

2. **Clone the Repository:** Use `git clone` to clone your forked repository to your local machine.

3. **Install Dependencies:** Run `flutter pub get` to install the necessary packages.

4. **Make your Changes:** Create a new branch and work on your changes.

5. **Test your Changes:** Run tests using `flutter test` to ensure everything works as expected.

6. **Submit a Pull Request (PR):** When ready, push your branch to your fork and create a PR from your branch to the main repository.

## Code Style and Guidelines

Please follow these guidelines when contributing code:

- Adhere to the [Dart style guide](https://dart.dev/guides/language/effective-dart/style).
- Write clear and concise commit messages that explain the purpose of your changes.
- Keep your code well-documented to help other contributors understand its purpose.

## Code of Conduct

QuickAlert follows the [Code of Conduct](CODE_OF_CONDUCT.md) to create a welcoming and inclusive community. Please review and abide by it in all interactions.

## Getting Help

If you have questions or need assistance, don't hesitate to reach out via [GitHub Issues](https://github.com/yourusername/QuickAlert/issues).

Let's collaborate and create amazing alert experiences with QuickAlert!
