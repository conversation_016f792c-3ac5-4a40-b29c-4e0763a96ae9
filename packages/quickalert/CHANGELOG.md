## 1.1.0

**Bug Fixes & Enhancements**
* Title Color not working [#25](https://github.com/belovance/QuickAlert/issues/25)
* Add option to dismiss alert with keyboard "Enter/Cancel" pressed [#27](https://github.com/belovance/QuickAlert/issues/27)
* Cancel button with QuickAlertType.loading [#31](https://github.com/belovance/QuickAlert/issues/31)

## 1.0.2

* Addition of more options to provide more control over the dialog
* Documentation Updated

## 1.0.1

* ReadME updated
* Documentation Typos Corrected

## 1.0.0

* Requested Updates
* Background Color Added
* Title Color Added
* Text Color Added
* Barrier Color Added

## 0.0.2

* ReadME updated
* Documentation Comments Added

## 0.0.1

* Initial Release
* Display animated alert dialogs such as success, error, warning, confirm, loading or even a custom dialog.
