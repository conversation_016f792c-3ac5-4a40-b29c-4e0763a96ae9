part of xr_helper;

class BaseCachedImage extends StatelessWidget {
  final String imageUrl;
  final double? height;
  final double? width;
  final BoxFit? fit;
  final double? radius;

  const BaseCachedImage(
    this.imageUrl, {
    super.key,
    this.height,
    this.width,
    this.fit = BoxFit.cover,
    this.radius,
  });

  @override
  Widget build(BuildContext context) {
    const radius = 100.0;

    const errorWidget = CircleAvatar(
      backgroundColor: Colors.grey,
      radius: 30,
      child: Icon(
        Icons.error,
        color: Colors.white,
      ),
    );

    return ClipRRect(
      borderRadius: const BorderRadius.all(Radius.circular(radius ?? 0)),
      child: CachedNetworkImage(
          imageUrl: imageUrl,
          fadeInDuration: const Duration(milliseconds: 0),
          fadeOutDuration: const Duration(milliseconds: 0),
          placeholder: (context, _) => loadingShimmerWidget(),
          height: height ?? 100,
          width: width ?? 100,
          fit: fit ?? BoxFit.cover,
          errorWidget: (context, url, error) => errorWidget).sized(
        height: height,
        width: width,
      ),
    );
  }

  Widget loadingShimmerWidget() => ClipRRect(
        borderRadius: const BorderRadius.all(Radius.circular(100)),
        child: Center(
          child: Shimmer(
            gradient: LinearGradient(
              colors: [
                Colors.grey[300]!,
                Colors.grey[100]!,
              ],
            ),
            child: Container(
                height: height,
                width: width,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: radius == null
                      ? null
                      : BorderRadius.all(Radius.circular(radius!)),
                )),
          ),
        ),
      );
}
