part of xr_helper;

class BaseMaterialApp extends StatelessWidget {
  final ThemeData? theme;
  final ThemeData? darkTheme;
  final ThemeMode? themeMode;
  final Locale? locale;
  final Iterable<LocalizationsDelegate<dynamic>>? localizationsDelegates;
  final Iterable<Locale> supportedLocales;
  final Widget home;
  final String title;
  final Widget? loadingWidget;

  const BaseMaterialApp({
    super.key,
    this.theme,
    this.darkTheme,
    this.themeMode,
    this.locale,
    this.localizationsDelegates,
    this.loadingWidget,
    this.supportedLocales = const <Locale>[Locale('en', 'US')],
    required this.home,
    this.title = '',
  });

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<AppConfig>(
      create: (BuildContext context) => AppConfig(),
      child: HookBuilder(builder: (context) {
        useEffect(() {
          WidgetsBinding.instance
              .addPostFrameCallback((_) => context.read<AppConfig>().init(
                    theme: theme,
                    loadingWidget: loadingWidget,
                  ));

          return () {};
        }, []);

        return MaterialApp(
          navigatorKey: NavigationService.navigationKey,

          debugShowCheckedModeBanner: false,
          //? Localization
          supportedLocales: supportedLocales,
          localizationsDelegates: localizationsDelegates,
          //? Theme
          theme: theme,
          darkTheme: darkTheme,
          themeMode: themeMode,
          title: title,
          locale: locale,
          home: HookBuilder(builder: (context) {
            useEffect(() {
              toastAlert = FToast();

              toastAlert.init(context);

              return () {};
            }, []);
            return home;
          }),
        );
      }),
    );
  }
}
