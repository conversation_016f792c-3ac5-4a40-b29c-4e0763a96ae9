part of xr_helper;

extension DateTimeExtensions on DateTime? {
  String get formatDateToString {
    if (this == null) return '';
    return '${this!.year}-${this!.month.toString().padLeft(2, '0')}-${this!.day.toString().padLeft(2, '0')}';
  }

  String get formatDateToMonthYearString {
    if (this == null) return '';
    return '${this!.year}-${this!.month.toString().padLeft(2, '0')}';
  }

  String get formatDateToStringWithTime {
    if (this == null) return '';
    return '${this!.year}-${this!.month.toString().padLeft(2, '0')}-${this!.day.toString().padLeft(2, '0')} ${this!.hour.toString().padLeft(2, '0')}:${this!.minute.toString().padLeft(2, '0')}:${this!.second.toString().padLeft(2, '0')}';
  }

  String get formatTime {
    if (this == null) return '';
    final hour = this!.hour % 12 == 0 ? 12 : this!.hour % 12;
    final minute = this!.minute.toString().padLeft(2, '0');
    final period = this!.hour >= 12 ? 'م' : 'ص';
    return '$hour:$minute $period';
  }

  String get formatDayName {
    if (this == null) return '';
    const daysInArabic = [
      'الأحد',
      'الإثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت'
    ];
    return daysInArabic[this!.weekday % 7];
  }

  bool isToday() {
    if (this == null) return false;
    final now = DateTime.now();
    return now.year == this!.year &&
        now.month == this!.month &&
        now.day == this!.day;
  }
}

// extension DateTimeExtentions on DateTime? {
//   String get formatDateToString {
//     if (this == null) return '';
//     return DateFormat('yyyy-MM-dd', 'en').format(this!);
//   }
//
//   String get formatDateToMonthYearString {
//     if (this == null) return '';
//     return DateFormat('yyyy-MM', 'en').format(this!);
//   }
//
//   String get formatDateToStringWithTime {
//     if (this == null) return '';
//     return DateFormat('yyyy-MM-dd hh:mm:ss', 'en').format(this!);
//   }
//
//   String get formatTime {
//     if (this == null) return '';
//     return DateFormat('hh:mm', 'en').format(this!) +
//         '' +
//         (this!.hour >= 12 ? 'م' : 'ص');
//   }
//
//   String get formatDayName {
//     if (this == null) return '';
//     return DateFormat('EEEE', 'ar').format(this!);
//   }
//
//   bool isToday() {
//     if (this == null) return false;
//     final now = DateTime.now();
//     return now.year == this!.year &&
//         now.month == this!.month &&
//         now.day == this!.day;
//   }
// }
