part of xr_helper;

class Log {
  static final _logger = Logger(
    printer: <PERSON><PERSON><PERSON><PERSON>(
      methodCount: 1,
      printEmojis: false,
      colors: false,
      noBoxingByDefault: true,
    ),
    filter: ProductionFilter(), // here!
  );
  // static final _logger = Logger();

  //? Info
  static void i(dynamic message) {
    _logger.i(message);
  }

  //? Warning
  static void w(dynamic message) {
    _logger.w(message);
  }

  //? Error
  static void e(dynamic message) {
    _logger.e(message);
  }

  //? Other Logs
  static void f(dynamic message) {
    _logger.f(message);
  }
}

// part of xr_helper;
//
// class Log {
//   static final _logger = Logger();
//
//   //? Info
//   static void i(dynamic message) {
//     if (kDebugMode) _logger.i(message);
//   }
//
//   //? Warning
//   static void w(dynamic message) {
//     if (kDebugMode) _logger.w(message);
//   }
//
//   //? Error
//   static void e(dynamic message) {
//     if (kDebugMode) _logger.e(message);
//   }
//
//   //? Other Logs
//   static void f(dynamic message) {
//     if (kDebugMode) _logger.f(message);
//   }
// }
