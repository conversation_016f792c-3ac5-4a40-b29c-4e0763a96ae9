part of xr_helper;

class AppTextStyles {
  static TextStyle headLine = const TextStyle(
    fontWeight: FontWeight.bold,
    fontSize: 28,
    color: Colors.black,
    fontFamily: 'Alex',
  );

  static TextStyle subHeadLine = const TextStyle(
    fontSize: 24,
    color: Colors.black,
    fontWeight: FontWeight.bold,
    fontFamily: 'Alex',
  );

  static TextStyle whiteSubHeadLine = const TextStyle(
    fontWeight: FontWeight.bold,
    fontSize: 24,
    color: Colors.white,
    fontFamily: '<PERSON>',
  );

  static TextStyle title = const TextStyle(
    color: Colors.black,
    fontSize: 18,
    fontWeight: FontWeight.bold,
    fontFamily: '<PERSON>',
  );
  static TextStyle primaryColorTitle = const TextStyle(
    color: Colors.black,
    fontSize: 18,
    fontWeight: FontWeight.bold,
    fontFamily: 'Alex',
  );

  static TextStyle whiteTitle = const TextStyle(
    color: Colors.white,
    fontSize: 18,
    fontWeight: FontWeight.bold,
    fontFamily: '<PERSON>',
  );

  static TextStyle subTitle = const TextStyle(
    color: Colors.black,
    fontSize: 16,
    fontFamily: '<PERSON>',
  );

  static TextStyle boldSubTitle = const TextStyle(
    color: Colors.black,
    fontWeight: FontWeight.bold,
    fontSize: 16,
    fontFamily: 'Alex',
  );

  static TextStyle whiteSubTitle = const TextStyle(
    color: Colors.white,
    fontFamily: 'Alex',
    fontSize: 16,
  );

  static TextStyle labelLarge = const TextStyle(
    color: Colors.black,
    fontSize: 14,
    fontFamily: 'Alex',
  );

  static TextStyle redLabelLarge = const TextStyle(
    color: Color(0xFFE74C3C),
    fontFamily: 'Alex',
    fontSize: 14,
  );

  static TextStyle greenLabelLarge = const TextStyle(
    color: Color(0xFF2ECC71),
    fontFamily: 'Alex',
    fontSize: 14,
  );

  static TextStyle whiteLabelLarge = const TextStyle(
    color: Colors.white,
    fontFamily: 'Alex',
    fontSize: 14,
  );

  static TextStyle labelMedium = const TextStyle(
    color: Colors.black,
    fontFamily: 'Alex',
    fontSize: 14,
  );

  static TextStyle whiteLabelMedium = const TextStyle(
    color: Colors.white,
    fontFamily: 'Alex',
    fontSize: 14,
  );

  static TextStyle labelSmall = const TextStyle(
    color: Colors.black,
    fontFamily: 'Alex',
    fontSize: 12,
  );

  static TextStyle body = const TextStyle(
    fontFamily: 'Alex',
    color: Colors.black,
  );

  static TextStyle whiteBody = const TextStyle(
    fontFamily: 'Alex',
    color: Colors.white,
  );

  static TextStyle hint = const TextStyle(
    fontFamily: 'Alex',
    color: Colors.black,
    fontSize: 16,
  );

  //! White hint
  static TextStyle whiteHint = const TextStyle(
    fontFamily: 'Alex',
    color: Colors.white,
    fontSize: 16,
  );

  //! Grey hint
  static TextStyle greyHint = const TextStyle(
    fontFamily: 'Alex',
    color: Colors.grey,
    fontSize: 15,
  );
}
