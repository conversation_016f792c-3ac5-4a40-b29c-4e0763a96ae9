# This is a generated file; do not edit or check into version control.
firebase_core=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.6.0/
firebase_core_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.18.1/
firebase_messaging=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.1.3/
firebase_messaging_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.9.2/
fluttertoast=/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.8/
path_provider=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
path_provider_android=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.15/
path_provider_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/
path_provider_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
path_provider_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.2.1/
sqflite=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/
sqflite_android=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.0/
sqflite_darwin=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/
url_launcher=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.2.1/
url_launcher_android=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.2.0/
url_launcher_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.2.1/
url_launcher_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.1.0/
url_launcher_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.1.0/
url_launcher_web=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.3.3/
url_launcher_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.0/
